"use client";
import "@/app/globals.css";

interface props {
  text: string;
  withArrow?: boolean;
  onClick?: () => void;
  classes?: string;
}

const PrimaryButton: React.FC<props> = ({ text, onClick, classes }) => {
  return (
    <button
      type="button"
      onClick={onClick}
      className={`${classes} flex items-center justify-center gap-2 py-4 cursor-pointer rounded-full bg-primary min-w-48 hover:bg-[#D45A08] transition`}
    >
      <span className="font-semibold text-lg text-white">{text}</span>
    </button>
  );
};

export default PrimaryButton;
