"use client";
import CustomButton from "./CustomButton";
import filterIcon from "../../../../public/svgs/icons8_filter 1.svg";
import downloadIcon from "../../../../public/svgs/icons8_download 1.svg";
import paymentIcon from "../../../../public/svgs/icons8_pay 1.svg";
import InvoiceTable from "./CustomDataTable";

const InvoicesDataTable = () => {
  return (
    <div>
      <div className="bg-white rounded-xl">
        <div className="flex items-center justify-between px-4 py-4">
          <h3 className="font-bold text-lg">Invoices for Manual Payment</h3>
          <CustomButton text="Filter" onClick={() => {}} icon={filterIcon} />
        </div>

        <InvoiceTable />

        <div className="p-4">
          <p className="text-dark font-bold">Total Amount Due: ر.س9014.0</p>
        </div>
      </div>

      <div className="flex items-center gap-3 my-4">
        <CustomButton
          text="Download Invoices"
          onClick={() => {}}
          icon={downloadIcon}
        />
        <CustomButton
          text="Make a Payment"
          onClick={() => {}}
          icon={paymentIcon}
        />
      </div>
    </div>
  );
};

export default InvoicesDataTable;
