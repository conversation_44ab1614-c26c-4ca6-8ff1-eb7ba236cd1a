import React, { useRef, useState } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import TickIcon from "./Icons/TickIcon";

interface CustomCheckboxProps {
  label: string;
  sup?: string;
  labelClass?: string;
  register?: UseFormRegisterReturn;
  error?: string;
  className?: string;
  id: string;
  name?: string;
  rootLableClassName?: string;
  value?: string;
  defaulChecked?: boolean;
  disabled?: boolean;
  disabledClass?: string;
  fadeOnDisable?: boolean;
}

const StepsCheckBoxRegister: React.FC<CustomCheckboxProps> = ({
  disabled = false,
  disabledClass,
  fadeOnDisable = false,
  label,
  register,
  error,
  className,
  id,
  labelClass,
  sup,
  rootLableClassName,
  value,
}) => {
  const inputRef = useRef<HTMLInputElement | null>(null);
  const [render, setRender] = useState<boolean>(false);

  const reRender = () => {
    setRender((prev) => !prev);
  };
  return (
    <div
      className={`flex justify-center flex-col gap-1 ${fadeOnDisable && disabled && "opacity-40"}`}
    >
      {false && render && <span></span>}
      <label
        htmlFor={id}
        className={` flex items-center gap-1.5 cursor-pointer ${rootLableClassName} flex-col-reverse gap-1`}
      >
        <input
          disabled={disabled}
          onClick={reRender}
          value={value}
          type="checkbox"
          id={id}
          {...register}
          className="peer hidden"
          ref={(el) => {
            register?.ref?.(el);
            inputRef.current = el;
          }}
        />
        <div
          className={`w-6 h-6 appearance-none flex justify-center items-center rounded-sm  bg-gray/20 peer-checked:bg-primary peer-checked:border-transparent ${disabled && disabledClass} ${className}`}
        >
          {inputRef.current?.checked && (
            <TickIcon fill="#FFFFFF" classess="w-3 h-3" />
          )}
        </div>
        <span
          className={`${labelClass} ${disabled && "font-bold"} ${inputRef.current?.checked && !disabled && "text-primary"} text-dark`}
        >
          {label}
          <sup>{sup}</sup>
        </span>
      </label>

      {error && <p className="text-red-500 text-sm">{error}</p>}
    </div>
  );
};

export default StepsCheckBoxRegister;
