"use client";

import React, { useEffect, useState } from "react";
import { useForm } from "react-hook-form";
import { z } from "zod";
import { zodResolver } from "@hookform/resolvers/zod";
import Image from "next/image";
import logo from "../../../public/images/logo1.png";
import CustomInput from "../reuseable/CustomInput";
import { useRouter } from "next/navigation";
import "../../app/globals.css";
import { toast } from "react-toastify";
import SquareCheckBox from "../reuseable/SquareCheckBox";
import { loginUser } from "@/utils/ApisHelperFunction";
import { setEncryptedToken } from "@/app/lib/auth";
import { storeIntegrityHash } from "@/app/lib/hashUtil";
import { API_ROUTES } from "@/utils/ApiRoutes";
import UnlockEmail from "./unlockemail";

const loginSchema = z.object({
  id: z
    .string()
    .min(1, "Username/Email is required")
    .email("Please enter a valid email address"),
  password: z.string().min(1, "Password is required"),
  remember: z.boolean().optional(),
});

type LoginFormValues = z.infer<typeof loginSchema>;

const Login = () => {
  const navigate = useRouter();
  const {
    register,
    handleSubmit,
    formState: { errors },
  } = useForm<LoginFormValues>({
    resolver: zodResolver(loginSchema),
    defaultValues: {
      id: "",
      password: "",
      remember: false,
    },
    mode: "onChange",
  });

  const [countdown, setCountdown] = useState<number>(0);
  const [isLocked, setIsLocked] = useState(false);
  const [unlockModal, setUnlockModal] = useState(false);
  const [unlockEmail, setUnlockEmail] = useState("");

  useEffect(() => {
    let timer: NodeJS.Timeout | null = null;
    if (countdown > 0) {
      timer = setInterval(() => {
        setCountdown((prev) => (prev > 0 ? prev - 1 : 0));
      }, 1000);
    }
    return () => {
      if (timer) clearInterval(timer);
    };
  }, [countdown]);

  const onSubmit = async (data: LoginFormValues) => {
    try {
      const result = await (data.remember === true
        ? loginUser(data.id, data.password, data.remember)
        : loginUser(data.id, data.password));

      if (result?.success && result.data) {
        await setEncryptedToken("AccessToken", result.data.accessToken ?? "", data.remember);

        if (result.data.user) {
          const userData = result.data.user;
          const fieldsToStore = [
            { key: "Role", value: userData.role },
            { key: "first_name", value: userData.first_name },
            { key: "last_name", value: userData.last_name },
            { key: "user_uuid", value: userData.user_uuid },
            { key: "username", value: userData.username },
            { key: "Email", value: userData.email },
            { key: "userid", value: userData.id },
            { key: "profile_image", value: userData.profile_image },
          ];

          fieldsToStore.forEach(async ({ key, value }) => {
            if (value) {
              await setEncryptedToken(key, String(value ?? ""))
            }
          });
        }
        storeIntegrityHash();
        toast.success("Login successful");
        // Prefer cookie-based ReturnTo set by middleware/client guard
        // let destination = "/dashboard";
        // try {
        //   const enc = Cookies.get("ReturnTo");
        //   if (enc) {
        //     const decoded = Decrytion(enc);
        //     if (decoded && decoded.startsWith("/")) {
        //       destination = decoded;
        //     }
        //     Cookies.remove("ReturnTo", { path: "/" });
        //   }
        // } catch {}
        navigate.push("/dashboard");
      } else {
        const message = result?.message || "";

        // ✅ Show static toast message
        toast.error("Invalid email or password");

        // ✅ Extract countdown seconds from backend message
        const match = message.match(/wait (\d+) seconds/i);
        if (match && match[1]) {
          const seconds = parseInt(match[1]);
          setCountdown(seconds);
        }

        // ✅ Detect locked account
        if (message.toLowerCase().includes("locked")) {
          setIsLocked(true);
        }
      }
    } catch {
      toast.error("Something went wrong, please try again.");
    }
  };

  const handleforgetpassword = () => {
    navigate.push("/forgot-password");
  };

  const handleUnlockRequest = async () => {
    if (!unlockEmail.trim()) {
      toast.error("Please enter your email before sending the request.");
      return;
    }
    try {
      const formData = new FormData();
      formData.append("email", unlockEmail);

      const res = await fetch(`${API_ROUTES.AUTH.UNLOCK_ACCOUNT}`, {
        method: "POST",
        body: formData,
      });

      const data = await res.json();

      if (data.success) {
        toast.success("Unlock link sent to your email.");
        setTimeout(() => {
          window.location.href = "/login";
        }, 1000);
      } else {
        toast.error("Failed to send unlock link. Please try again.");
      }

      setUnlockModal(false);
      setUnlockEmail("");
    } catch {
      toast.error("Failed to send unlock request");
    }
  };

  return (
    <>
      <div className="min-h-screen authbackground bg-cover bg-center bg-no-repeat flex items-center justify-center relative overflow-hidden">
        <div className="w-[30%] max-md:w-[80%] md:w-[50%] lg:w-[40%] xl:w-[30%] 2xl:w-[29%] z-30">
          <div className="xl:mb-3 lg:mb-5 2xl:mb-14">
            <Image
              src={logo}
              alt="Aligners Logo"
              className="mx-auto mb-2 w-44 xl:w-52"
              width={1000}
              height={1000}
            />
          </div>
          <div className="z-10 w-full 2xl:p-[50px] lg:p-[25px] p-[20px] rounded-[40px] bg-[#4444430F] text-center border border-[#44444321] backdrop-blur-sm">
            <h1 className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-[45px] pb-[20px] lg:pb-[8px] z-40">
              Welcome to the <br />
              <span className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-[45px]">
                Graphy<sup>®</sup> Doctor Site
              </span>
            </h1>
            {countdown > 0 && (
              <p className="text-red-500 text-sm mb-2">
                Please wait {countdown}s before next attempt.
              </p>
            )}
            <form
              onSubmit={handleSubmit(onSubmit)}
              noValidate
              className=" text-left pt-4  z-40"
            >
              <div className="space-y-2">
                <CustomInput
                  className="!px-5 !py-[16px] !2xl:py-[18px] lg:py-3"
                  type="email"
                  placeholder="Username/Email"
                  register={register("id")}
                  error={errors.id?.message}
                />
                <CustomInput
                  className="!px-5 !py-[16px] !2xl:py-[18px] lg:py-3"
                  type="password"
                  placeholder="Password"
                  register={register("password")}
                  error={errors.password?.message}
                />
                {isLocked && (
                  <p
                    className="text-red-600 text-sm cursor-pointer hover:underline"
                    onClick={() => setUnlockModal(true)}
                  >
                    Your account is locked. Click here to verify your email.
                  </p>
                )}
                <SquareCheckBox
                  id="remember"
                  label="Remember username/email"
                  register={register("remember")}
                />
              </div>
              <div className="pt-5 lg:pt-3 xl:pt-9">
                <button
                  type="submit"
                  className={`w-full py-4 lg:py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition cursor-pointer font-medium`}
                  disabled={countdown > 0}
                >
                  Login
                </button>
              </div>
            </form>
            <div className="xl:mt-[8px] 2xl:mt-[14px]">
              <button
                className="text-sm text-[#F00] hover:underline cursor-pointer"
                onClick={handleforgetpassword}
              >
                Forgot Username or Password?
              </button>
            </div>
          </div>
        </div>
      </div>
      <div className="absolute bottom-1 w-full">
        <p className="mt-auto text-xs mx-auto w-fit text-gray-400">
          © 2005–2025 Graphy Inc. All rights reserved.
        </p>
      </div>

      {unlockModal && (
        <UnlockEmail
          unlockEmail={unlockEmail}
          setUnlockEmail={setUnlockEmail}
          onSubmit={handleUnlockRequest}
          onClose={() => setUnlockModal(false)}
        />
      )}
    </>
  );
};

export default Login;