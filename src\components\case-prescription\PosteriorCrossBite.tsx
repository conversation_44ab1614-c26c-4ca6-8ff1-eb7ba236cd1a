import { FieldValues, Path } from "react-hook-form";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { SpecialProps } from "./MovementResctiction_2";

const PosteriorCrossBite = <T extends FieldValues>({
  register,
  errors,
  watch,
  //  setValue,
  number,
}: SpecialProps<T>) => {
  const option = watch("posteriorCrossBite.option" as Path<T>);
  return (
    <div>
      <p className="text-lg font-semibold text-gray-800">
        {`${number}`} Posterior Crossbite
      </p>
      <div className="mt-3 flex gap-3">
        <RoundRadioButton
          id="posterior-crossbite-not-correct-1"
          label="Do not correct"
          value="doNotCorrect"
          register={register}
          name="posteriorCrossBite.option"
          labelClass="!text-[#434343] text-base"
        />
        <RoundRadioButton
          id="posterior-crossbite-correct-2"
          label="Correct"
          value="correct"
          register={register}
          name="posteriorCrossBite.option"
          labelClass="!text-[#434343] text-base"
        />
        {typeof errors.prompt === "object" &&
          errors.prompt !== null &&
          "message" in errors.prompt &&
          errors.prompt.message && (
            <p className="text-red-500 text-sm mt-1">
              {String(errors.prompt.message)}
            </p>
          )}
      </div>
      {option == "correct" && (
        <div className="ps-4 flex gap-3 mt-3">
          <StepsCheckBoxRegister
            id="posterior-crossbite-location-right"
            label="Right"
            value="right"
            register={register("posteriorCrossBite.location" as Path<T>)}
            rootLableClassName="!flex-row"
            labelClass="!text-[#434343] text-base"
          />
          <StepsCheckBoxRegister
            id="posterior-crossbite-location-left"
            label="Left"
            value="left"
            register={register("posteriorCrossBite.location" as Path<T>)}
            rootLableClassName="!flex-row"
            labelClass="!text-[#434343] text-base"
          />
          {typeof errors.extraction === "object" &&
            errors.extraction !== null &&
            "extractionTeeth" in errors.extraction &&
            typeof errors.extraction.extractionTeeth === "object" &&
            errors.extraction.extractionTeeth !== null &&
            "message" in errors.extraction.extractionTeeth &&
            errors.extraction.extractionTeeth.message && (
              <p className="text-red-500 text-sm mt-1">
                {String(errors.extraction.extractionTeeth.message)}
              </p>
            )}
        </div>
      )}
    </div>
  );
};

export default PosteriorCrossBite;
