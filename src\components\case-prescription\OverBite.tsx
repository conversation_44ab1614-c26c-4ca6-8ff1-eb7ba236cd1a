"use client";
import { capitalizeFirstWord } from "@/utils/helperFunctions";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { SpecialProps } from "./MovementResctiction_2";
import { useCallback, useEffect } from "react";
import { AnimatePresence, motion } from "framer-motion";
import { DefaultTransition } from "./ArchToTreat_1";
import { FieldValues, Path, PathValue } from "react-hook-form";

type OverbiteSide = {
  options: string[];
  otherNote: string;
  others: boolean;
  anteriorBiteRamps: string[];
};

// type OverbiteType = {
//     option: string;
//     upper: OverbiteSide;
//     lower: OverbiteSide;
// };

const Overbite = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  const overbiteOption = watch("overbite.option" as Path<T>);
  const otherSelectedUpper = watch("overbite.upper.others" as Path<T>);
  const otherSelectedLower = watch("overbite.lower.others" as Path<T>);
  const correctOpenBiteOptionsUpper = watch(
    "overbite.upper.options" as Path<T>,
  );
  const correctOpenBiteOptionsLower = watch(
    "overbite.lower.options" as Path<T>,
  );

  // Helper for default OverbiteSide
  const defaultOverbiteSide: OverbiteSide = {
    options: [],
    otherNote: "",
    others: false,
    anteriorBiteRamps: [],
  };

  // Reset logic
  useEffect(() => {
    setValue(
      "overbite.lower" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.lower.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
  }, [setValue, overbiteOption]);

  useEffect(() => {
    setValue(
      "overbite.lower" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.lower.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
  }, [setValue, overbiteOption]);

  const handleOverbiteOptionChange = useCallback(() => {
    setValue(
      "overbite.lower" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.lower.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper.anteriorBiteRamps" as Path<T>,
      [] as PathValue<T, Path<T>>,
    );
    setValue(
      "overbite.upper" as Path<T>,
      defaultOverbiteSide as PathValue<T, Path<T>>,
    );
  }, [setValue]);

  const handleOtherSelectedUpperChange = useCallback(() => {
    if (otherSelectedUpper) {
      setValue(
        "overbite.upper.options" as Path<T>,
        [] as PathValue<T, Path<T>>,
      );
    }
  }, [otherSelectedUpper, setValue]);

  const handleCorrectOpenBiteOptionsUpperChange = useCallback(() => {
    if (
      correctOpenBiteOptionsUpper &&
      correctOpenBiteOptionsUpper?.length > 0
    ) {
      setValue(
        "overbite.upper.others" as Path<T>,
        false as PathValue<T, Path<T>>,
      );
    }
  }, [correctOpenBiteOptionsUpper, setValue]);

  const handleOtherSelectedLowerChange = useCallback(() => {
    if (otherSelectedLower) {
      setValue(
        "overbite.lower.options" as Path<T>,
        [] as PathValue<T, Path<T>>,
      );
    }
  }, [otherSelectedLower, setValue]);

  const handleCorrectOpenBiteOptionsLowerChange = useCallback(() => {
    if (
      correctOpenBiteOptionsLower &&
      correctOpenBiteOptionsLower?.length > 0
    ) {
      setValue(
        "overbite.lower.others" as Path<T>,
        false as PathValue<T, Path<T>>,
      );
    }
  }, [correctOpenBiteOptionsLower, setValue]);

  useEffect(() => {
    handleOverbiteOptionChange();
  }, [overbiteOption, handleOverbiteOptionChange]);

  useEffect(() => {
    handleOtherSelectedUpperChange();
  }, [handleOtherSelectedUpperChange]);

  useEffect(() => {
    handleCorrectOpenBiteOptionsUpperChange();
  }, [handleCorrectOpenBiteOptionsUpperChange]);

  useEffect(() => {
    handleOtherSelectedLowerChange();
  }, [handleOtherSelectedLowerChange]);

  useEffect(() => {
    handleCorrectOpenBiteOptionsLowerChange();
  }, [handleCorrectOpenBiteOptionsLowerChange]);

  return (
    <div>
      <p className="text-lg font-semibold text-gray-800">
        {`${number}`} Overbite
      </p>
      <div className="mt-3 space-y-3">
        <div className="flex items-center gap-2">
          <RoundRadioButton
            id="overbite-showResulting"
            label="Decide after 3D Simulation"
            value="Decide after 3D Simulation"
            register={register}
            name="overbite.option"
            labelClass="!text-[#434343] text-base"
          />
          <RoundRadioButton
            id="overbite-maintainInitial"
            label="Do not Correct"
            value="Do not Correct"
            register={register}
            name="overbite.option"
            labelClass="!text-[#434343] text-base"
          />
          <RoundRadioButton
            id="overbite-improveOpen"
            label="Correct Open Bite"
            value="Correct Open Bite"
            register={register}
            name="overbite.option"
            labelClass="!text-[#434343] text-base"
          />
          <RoundRadioButton
            id="overbite-improveDeep"
            label="Improve Deep Bite"
            value="Improve Deep Bite"
            register={register}
            name="overbite.option"
            labelClass="!text-[#434343] text-base"
          />
        </div>

        <div className="ps-6">
          <AnimatePresence initial={false} mode="wait">
            {overbiteOption === "Correct Open Bite" && (
              <motion.div
                key="upper"
                initial={{ height: 0, opacity: 0 }}
                animate={{ height: "auto", opacity: 1 }}
                exit={{ height: 0, opacity: 0 }}
                transition={DefaultTransition}
                style={{ overflow: "hidden" }}
              >
                <div className="flex flex-col gap-2">
                  {["upper", "lower"].map((side: string, index: number) => {
                    return (
                      <div key={index}>
                        <div key={index} className="flex flex-col gap-5">
                          <span>{capitalizeFirstWord(side)}</span>
                          <div className="flex gap-3 ps-5">
                            <StepsCheckBoxRegister
                              id={`Extrude premolars-${side}-${index}`}
                              label="Extrude premolars"
                              value="Extrude premolars"
                              register={register(
                                `overbite.${side}.options` as unknown as Path<T>,
                              )}
                              name={`overbite.${side}.options`}
                              labelClass="!text-[#434343] text-base"
                              rootLableClassName="!flex-row"
                            />
                            <StepsCheckBoxRegister
                              id={`Intrude Posterior Teeth-${side}-${index}`}
                              label="Intrude Posterior Teeth"
                              value="Intrude Posterior Teeth"
                              register={register(
                                `overbite.${side}.options` as unknown as Path<T>,
                              )}
                              name={`overbite.${side}.options`}
                              rootLableClassName="!flex-row"
                              labelClass="!text-[#434343] text-base"
                            />
                            <StepsCheckBoxRegister
                              id={`Others(please note in special remarks)-${side}-${index}`}
                              label="Others(please note in special remarks)"
                              register={register(
                                `overbite.${side}.others` as unknown as Path<T>,
                              )}
                              name={`overbite.${side}.others`}
                              rootLableClassName="!flex-row"
                              labelClass="!text-[#434343] text-base"
                            />
                          </div>
                        </div>
                        {watch(`overbite.${side}.others` as Path<T>) && (
                          <div>
                            <textarea
                              placeholder="Note"
                              {...register(
                                `overbite.${side}.otherNote` as unknown as Path<T>,
                              )}
                              className="border p-2 w-full rounded-xl mt-2"
                            />
                          </div>
                        )}
                      </div>
                    );
                  })}
                </div>
              </motion.div>
            )}
          </AnimatePresence>
        </div>

        <div className="ps-6">
          {overbiteOption === "Improve Deep Bite" && (
            <div className="flex flex-col gap-2">
              {["upper"].map((side: string, index: number) => {
                const selectedOptions = watch(
                  `overbite.${side}.options` as Path<T>,
                );
                return (
                  <div key={index}>
                    <div className="flex flex-col gap-5">
                      <span>{capitalizeFirstWord(side)}</span>
                      <div className="flex gap-2">
                        <StepsCheckBoxRegister
                          id={`Anterior Intrusion-${side}-${index}`}
                          label="Anterior Intrusion"
                          value="Anterior Intrusion"
                          register={register(
                            `overbite.${side}.options` as Path<T>,
                          )}
                          name={`overbite.${side}.options`}
                          labelClass="!text-[#434343] text-base"
                          rootLableClassName="!flex-row"
                        />
                        <StepsCheckBoxRegister
                          id={`Extrude premolars-${side}-${index}`}
                          label="Extrude premolars"
                          value="Extrude premolars"
                          register={register(
                            `overbite.${side}.options` as Path<T>,
                          )}
                          name={`overbite.${side}.options`}
                          rootLableClassName="!flex-row"
                          labelClass="!text-[#434343] text-base"
                        />
                        <StepsCheckBoxRegister
                          id={`Anterior Bite Ramps (Optimized for Deep overbite with moderate overjet case)-${side}-${index}`}
                          label="Anterior Bite Ramps (Optimized for Deep overbite with moderate overjet case)"
                          value="Anterior Bite Ramps"
                          register={register(
                            `overbite.${side}.options` as Path<T>,
                          )}
                          name={`overbite.${side}.options`}
                          rootLableClassName="!flex-row"
                          labelClass="!text-[#434343] text-base"
                        />
                        <StepsCheckBoxRegister
                          id={`Others(please note in special remarks)-${side}-${index}`}
                          label="Others(please note in special remarks)"
                          register={register(
                            `overbite.${side}.others` as Path<T>,
                          )}
                          name={`overbite.${side}.others`}
                          rootLableClassName="!flex-row"
                          labelClass="!text-[#434343] text-base"
                        />
                      </div>
                      {Array.isArray(selectedOptions) &&
                        selectedOptions?.includes("Anterior Bite Ramps") && (
                          <div className="ps-6 flex gap-2">
                            <StepsCheckBoxRegister
                              id={`Central incisor-${side}-${index}`}
                              label="Central incisor"
                              value="Central incisor"
                              register={register(
                                `overbite.${side}.anteriorBiteRamps` as Path<T>,
                              )}
                              name={`overbite.${side}.anteriorBiteRamps`}
                              labelClass="!text-[#434343] text-base"
                              rootLableClassName="!flex-row"
                            />
                            <StepsCheckBoxRegister
                              id={`Lateral incisor-${side}-${index}`}
                              label="Lateral incisor"
                              value="Lateral incisor"
                              register={register(
                                `overbite.${side}.anteriorBiteRamps` as Path<T>,
                              )}
                              name={`overbite.${side}.anteriorBiteRamps`}
                              labelClass="!text-[#434343] text-base"
                              rootLableClassName="!flex-row"
                            />
                            <StepsCheckBoxRegister
                              id={`Canine-${side}-${index}`}
                              label="Canine"
                              value="Canine"
                              register={register(
                                `overbite.${side}.anteriorBiteRamps` as Path<T>,
                              )}
                              name={`overbite.${side}.anteriorBiteRamps`}
                              labelClass="!text-[#434343] text-base"
                              rootLableClassName="!flex-row"
                            />
                          </div>
                        )}
                    </div>
                    {watch(`overbite.${side}.others` as Path<T>) && (
                      <div>
                        <textarea
                          placeholder="Note"
                          {...register(`overbite.${side}.otherNote` as Path<T>)}
                          className="border p-2 w-full rounded-xl mt-2"
                        />
                      </div>
                    )}
                  </div>
                );
              })}

              {["lower"].map((side: string, index: number) => {
                return (
                  <div key={index}>
                    <div key={index} className="flex flex-col gap-5">
                      <span>{capitalizeFirstWord(side)}</span>
                      <div className="flex gap-3 ps-5">
                        <StepsCheckBoxRegister
                          id={`Anterior Intrusion-${side}-${index}`}
                          label="Anterior Intrusion"
                          value="Anterior Intrusion"
                          register={register(
                            `overbite.${side}.options` as Path<T>,
                          )}
                          name={`overbite.${side}.options`}
                          labelClass="!text-[#434343] text-base"
                          rootLableClassName="!flex-row"
                        />
                        <StepsCheckBoxRegister
                          id={`Extrude premolars-${side}-${index}`}
                          label="Extrude premolars"
                          value="Extrude premolars"
                          register={register(
                            `overbite.${side}.options` as Path<T>,
                          )}
                          name={`overbite.${side}.options`}
                          rootLableClassName="!flex-row"
                          labelClass="!text-[#434343] text-base"
                        />
                        <StepsCheckBoxRegister
                          id={`Others(please note in special remarks)-${side}-${index}`}
                          label="Others(please note in special remarks)"
                          register={register(
                            `overbite.${side}.others` as Path<T>,
                          )}
                          name={`overbite.${side}.others`}
                          rootLableClassName="!flex-row"
                          labelClass="!text-[#434343] text-base"
                        />
                      </div>
                    </div>
                    {watch(`overbite.${side}.others` as Path<T>) && (
                      <div>
                        <textarea
                          placeholder="Note"
                          {...register(`overbite.${side}.otherNote` as Path<T>)}
                          className="border p-2 w-full rounded-xl mt-2"
                        />
                      </div>
                    )}
                  </div>
                );
              })}
            </div>
          )}
        </div>
      </div>
      {typeof errors.overbite === "object" &&
        errors.overbite !== null &&
        "option" in errors.overbite &&
        errors.overbite.option &&
        typeof errors.overbite.option === "object" &&
        errors.overbite.option !== null &&
        "message" in errors.overbite.option &&
        errors.overbite.option.message && (
          <p className="text-red-500 text-sm mt-1">
            {String(errors.overbite.option.message)}
          </p>
        )}
    </div>
  );
};

export default Overbite;
