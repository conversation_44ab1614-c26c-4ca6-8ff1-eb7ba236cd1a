import React from "react";
import { UseFormRegisterReturn } from "react-hook-form";

interface CustomCheckboxProps {
  id: string;
  label?: string; // Make label optional
  register: UseFormRegisterReturn;
  className?: string;
}

const CustomCheckbox: React.FC<CustomCheckboxProps> = ({
  id,
  label,
  register,
  className = "",
}) => {
  return (
    <div className={`flex items-center ${className}`}>
      <input type="checkbox" id={id} {...register} className="mr-2" />
      {label && ( // Render label only if it's provided
        <label htmlFor={id} className="text-sm text-gray-600">
          {label}
        </label>
      )}
    </div>
  );
};

export default CustomCheckbox;
