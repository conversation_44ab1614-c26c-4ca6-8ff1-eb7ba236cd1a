import SpecialistProfile from "@/components/account/SpecialistProfile/SpecialistProfile"
import { fetchApi } from "@/api/getapis"
import { API_SERVER_ROUTES } from "@/utils/ApiRoutes"
import type { SpecialistProfileApiResponse } from "@/types/types"
export const dynamic = "force-dynamic";
const page = async () => {
  let profileData: SpecialistProfileApiResponse | null = null
  let error: string | null = null

  try {
    const response = await fetchApi(API_SERVER_ROUTES.PROFILE.GET_PROFILE)
    if (response && typeof response === "object") {
      const typedResponse = response as { success: boolean; data?: SpecialistProfileApiResponse; message?: string }

      if (typedResponse.success) {
        profileData = typedResponse.data || null
      } else {
        error = typedResponse.message || "Failed to fetch profile data"
      }
    } else {
      error = "Failed to fetch profile data - no response received"
    }
  } catch (err) {
    console.error("Error fetching specialist profile:", err)
    error = "An error occurred while fetching profile data"
  }

  return (
    <>
      <SpecialistProfile initialData={profileData} error={error} />
    </>
  )
}

export default page
