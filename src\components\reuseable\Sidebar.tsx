"use client";
import Image from "next/image";
import { useState, useEffect } from "react";
import logo from "../../../public/images/logo.png";
import Doublearrow from "./Icons/Doublearrow";
import { useRouter, usePathname } from "next/navigation";
import { toast } from "react-toastify";
import {clearAllCookies, getDecryptedToken, logout } from "@/app/lib/auth";
interface SidebarProps {
  isOpen: boolean;
  toggleSidebar: () => void;
}
export default function Sidebar({ isOpen, toggleSidebar }: SidebarProps) {
  // const [isOpen, setIsOpen] = useState(true);
  const [selectedOption, setSelectedOption] = useState<string | null>(null);
  const [onHover, setOnHover] = useState<string | null>(null);
  const navigate = useRouter();
  const pathname = usePathname();
  const Role = getDecryptedToken("Role");

  // Map routes to sidebar options
  const routeToOption = (path: string) => {
    if (path.startsWith("/dashboard")) return "Patients";
    if (path.startsWith("/account")) return "Account";
    // Add more mappings as needed
    return null;
  };

  // Sync selectedOption with current route
  useEffect(() => {
    const option = routeToOption(pathname);
    if (option && option !== selectedOption) {
      setSelectedOption(option);
      localStorage.setItem("selectedOption", option);
    }
  }, [pathname]);

  useEffect(() => {
    const savedOption = localStorage.getItem("selectedOption");
    if (savedOption) {
      setSelectedOption(savedOption);
    } else {
      setSelectedOption("Patients");
    }
  }, []);

  const handleOptionClick = (option: string) => {
    setSelectedOption(option);
    localStorage.setItem("selectedOption", option);
    if (option === "Patients") {
      navigate.push("/dashboard");
      console.log(option);
    } else if (option === "Account") {
      navigate.push("/account/summary");
    } else if (option === "Support") {
      console.log(option);
    }
  };

  const handleLogout = () => {
    toast.success("Logout successfully");
    // Use the logout function from auth library for consistent behavior
    setTimeout(() => {
      logout();
    }, 500); // Small delay to show the success toast
  };

  return (
    <div
      className={`transition-all  p-2 duration-300 ${
        isOpen ? "w-72" : "w-20"
      } max-md:w-20 `}
    >
      <div className="flex flex-col justify-between bg-white h-[calc(100vh_-_1rem)] rounded-[16px] ">
        {/* Sidebar Header */}
        <div>
          <div>
            <div className="flex items-center justify-between p-5 ">
              {isOpen && (
                <div
                  onClick={() => navigate.push("/dashboard")}
                  className="cursor-pointer"
                >
                  <Image
                    className="w-40 max-md:hidden"
                    src={logo}
                    alt=".."
                    width={1000}
                    height={1000}
                  />
                </div>
              )}
              <button
                type="button"
                aria-label="Toggle Sidebar"
                onClick={toggleSidebar}
                className="text-xl cursor-pointer max-md:hidden"
              >
                <Doublearrow isOpen={isOpen} />
              </button>
            </div>

            {/* Sidebar Links */}
            <div
              className={`flex flex-col gap-1 ${isOpen ? "p-5" : "p-2"} text-gray-700 max-md:p-2`}
            >
              {/* Patients Option */}
              <div
                onClick={() => handleOptionClick("Patients")}
                onMouseOver={() => setOnHover("Patients")}
                onMouseLeave={() => setOnHover(null)}
                className={`flex items-center gap-3 p-3 rounded-full cursor-pointer transition-all ${
                  selectedOption === "Patients"
                    ? "bg-[#FFE5D4] text-orange-500"
                    : "hover:bg-[#FFE5D4] hover:text-orange-500"
                }`}
              >
                {selectedOption === "Patients" || onHover === "Patients" ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M8 2.5C6.07 2.5 4.5 4.07 4.5 6C4.5 7.93 6.07 9.5 8 9.5C9.93 9.5 11.5 7.93 11.5 6C11.5 4.07 9.93 2.5 8 2.5ZM16 2.5C14.07 2.5 12.5 4.07 12.5 6C12.5 7.93 14.07 9.5 16 9.5C17.93 9.5 19.5 7.93 19.5 6C19.5 4.07 17.93 2.5 16 2.5ZM4.75 10.5C3.785 10.5 3 11.285 3 12.25V17C3 19.757 5.243 22 8 22C9.271 22 10.4295 21.5199 11.3125 20.7354C10.4935 19.7094 10 18.412 10 17V12.25C10 11.586 10.2374 10.976 10.6309 10.5H4.75ZM12.75 10.5C11.785 10.5 11 11.285 11 12.25V17C11 19.757 13.243 22 16 22C18.757 22 21 19.757 21 17V12.25C21 11.285 20.215 10.5 19.25 10.5H12.75Z"
                      fill="#EB6309"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-orange-500"
                  >
                    <g opacity="0.6">
                      <path
                        d="M8 2.5C6.07589 2.5 4.5 4.07589 4.5 6C4.5 7.92411 6.07589 9.5 8 9.5C9.92411 9.5 11.5 7.92411 11.5 6C11.5 4.07589 9.92411 2.5 8 2.5ZM16 2.5C14.0759 2.5 12.5 4.07589 12.5 6C12.5 7.92411 14.0759 9.5 16 9.5C17.9241 9.5 19.5 7.92411 19.5 6C19.5 4.07589 17.9241 2.5 16 2.5ZM8 4C9.11345 4 10 4.88655 10 6C10 7.11345 9.11345 8 8 8C6.88655 8 6 7.11345 6 6C6 4.88655 6.88655 4 8 4ZM16 4C17.1135 4 18 4.88655 18 6C18 7.11345 17.1135 8 16 8C14.8865 8 14 7.11345 14 6C14 4.88655 14.8865 4 16 4ZM4.75 10.5C3.785 10.5 3 11.285 3 12.25V17C3 19.757 5.243 22 8 22C9.271 22 10.4295 21.5199 11.3125 20.7354C10.9945 20.3369 10.7261 19.8973 10.5166 19.4258C9.8796 20.0868 8.988 20.5 8 20.5C6.07 20.5 4.5 18.93 4.5 17V12.25C4.5 12.112 4.612 12 4.75 12H10.0127C10.0642 11.4335 10.2864 10.9155 10.6299 10.5H4.75ZM12.75 10.5C11.7925 10.5 11 11.2925 11 12.25V17C11 19.7524 13.2476 22 16 22C18.7524 22 21 19.7524 21 17V12.25C21 11.2925 20.2075 10.5 19.25 10.5H12.75ZM12.75 12H19.25C19.3975 12 19.5 12.1025 19.5 12.25V17C19.5 18.9416 17.9416 20.5 16 20.5C14.0584 20.5 12.5 18.9416 12.5 17V12.25C12.5 12.1025 12.6025 12 12.75 12Z"
                        fill="#999999"
                      />
                    </g>
                  </svg>
                )}
                {isOpen && (
                  <span className="text-gray-700 max-md:hidden">Patients</span>
                )}
              </div>

              {/* Account Option */}
              <div
                onClick={() => {
                  handleOptionClick("Account");
                  if (Role !== "specialist") {
                    navigate.push("/account/dr-profile");
                  } else if (Role === "specialist") {
                    navigate.push("/account/specialist-profile");
                  }
                }}
                onMouseOver={() => setOnHover("Account")}
                onMouseLeave={() => setOnHover(null)}
                className={`flex items-center gap-3 p-3 rounded-full cursor-pointer transition-all ${
                  selectedOption === "Account"
                    ? "bg-[#FFE5D4] text-orange-500"
                    : "hover:bg-[#FFE5D4] hover:text-orange-500"
                }`}
              >
                {selectedOption === "Account" || onHover === "Account" ? (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                  >
                    <path
                      d="M12 2C9.243 2 7 4.243 7 7C7 9.757 9.243 12 12 12C14.757 12 17 9.757 17 7C17 4.243 14.757 2 12 2ZM20.7568 12C20.1825 12.0001 19.6086 12.2185 19.1719 12.6553L13.1631 18.6631C13.0076 18.8181 12.895 19.0117 12.835 19.2227L12.0293 22.043C11.9548 22.3045 12.0267 22.5868 12.2197 22.7793C12.3622 22.9218 12.5545 22.999 12.751 22.999C12.82 22.999 12.8881 22.9892 12.9561 22.9697L15.7783 22.1641C15.9903 22.1036 16.1839 21.9894 16.3359 21.8359L22.3438 15.8281C22.7668 15.4041 23 14.8402 23 14.2412C23 13.6422 22.7663 13.0788 22.3428 12.6553C21.9055 12.218 21.3312 11.9999 20.7568 12ZM6.25 14C5.0095 14 4 15.0095 4 16.25V16.6318C4 17.8088 4.48975 18.9399 5.34375 19.7354C6.34575 20.6679 8.12239 21.7589 11.0254 21.9629C11.0364 21.8979 11.0494 21.8331 11.0674 21.7686L11.8721 18.9473C11.9786 18.5733 12.181 18.2296 12.457 17.9551L16.4131 14H6.25Z"
                      fill="#EB6309"
                    />
                  </svg>
                ) : (
                  <svg
                    xmlns="http://www.w3.org/2000/svg"
                    width="24"
                    height="24"
                    viewBox="0 0 24 24"
                    fill="none"
                    className="text-orange-500"
                  >
                    <g opacity="0.6">
                      <path
                        d="M12 2C9.24746 2 7 4.24746 7 7C7 9.75254 9.24746 12 12 12C14.7525 12 17 9.75254 17 7C17 4.24746 14.7525 2 12 2ZM12 3.5C13.9419 3.5 15.5 5.05812 15.5 7C15.5 8.94188 13.9419 10.5 12 10.5C10.0581 10.5 8.5 8.94188 8.5 7C8.5 5.05812 10.0581 3.5 12 3.5ZM20.7568 12C20.1825 12.0001 19.6086 12.2185 19.1719 12.6553L13.1631 18.6631C13.0076 18.8181 12.895 19.0117 12.835 19.2227L12.0293 22.043C11.9548 22.3045 12.0267 22.5868 12.2197 22.7793C12.3622 22.9218 12.5545 22.999 12.751 22.999C12.82 22.999 12.8881 22.9892 12.9561 22.9697L15.7783 22.1641C15.9903 22.1036 16.1839 21.9894 16.3359 21.8359L22.3438 15.8281C22.7668 15.4041 23 14.8402 23 14.2412C23 13.6422 22.7663 13.0788 22.3428 12.6553C21.9055 12.218 21.3312 11.9999 20.7568 12ZM6.25 14C5.0095 14 4 15.0095 4 16.25V16.6318C4 17.8088 4.48975 18.9399 5.34375 19.7354C6.34625 20.6679 8.12287 21.7594 11.0264 21.9639C11.0369 21.8984 11.0489 21.8331 11.0674 21.7686L11.4336 20.4873C8.77859 20.3693 7.21971 19.4312 6.36621 18.6367C5.81571 18.1242 5.5 17.3938 5.5 16.6318V16.25C5.5 15.8365 5.8365 15.5 6.25 15.5H14.9131L16.4131 14H6.25Z"
                        fill="#999999"
                      />
                    </g>
                  </svg>
                )}
                {isOpen && (
                  <span className="text-gray-700 max-md:hidden">Account</span>
                )}
              </div>

              {/* Support Option */}
              {/* <div
                                onClick={() => handleOptionClick('Support')}
                                onMouseOver={() => setOnHover('Support')}
                                onMouseLeave={() => setOnHover(null)}
                                className={`flex items-center gap-3 p-3 rounded-full cursor-pointer transition-all ${selectedOption === 'Support' ? 'bg-[#FFE5D4] text-orange-500' : 'hover:bg-[#FFE5D4] hover:text-orange-500'
                                    }`}
                            >
                                {selectedOption === 'Support' || onHover === 'Support' ? (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <path d="M12.25 2C7.397 2 4.5 4.71 4.5 9.25V11.0107C3.8735 11.4752 3.5 12.201 3.5 13C3.5 14.1785 4.31945 15.1686 5.41895 15.4316C6.41545 19.3831 9.013 22 12 22C12.414 22 12.75 21.6645 12.75 21.25C12.75 20.8355 12.414 20.5 12 20.5C9.647 20.5 7.49367 18.0708 6.76367 14.5928C6.69067 14.2458 6.38527 13.9971 6.03027 13.9971C6.01327 13.9971 5.9917 13.9975 5.9707 13.999C5.4327 13.984 5 13.5415 5 13C5 12.6175 5.21927 12.2769 5.57227 12.1094C5.83377 11.9849 6 11.7211 6 11.4316V10.0869C7.9275 9.58091 9.13432 8.55201 9.77832 7.83301C10.5988 8.33351 12.2155 9 15 9C16.0734 9 17.0048 8.90345 17.748 8.78223C17.8657 9.13457 18 9.65093 18 10.25V15.25C18 16.5017 17.0017 17.5 15.75 17.5H14.8301C14.6882 17.201 14.4645 16.9483 14.1848 16.7713C13.9051 16.5943 13.581 16.5002 13.25 16.5C13.0202 16.5 12.7926 16.5453 12.5803 16.6332C12.368 16.7212 12.1751 16.8501 12.0126 17.0126C11.8501 17.1751 11.7212 17.368 11.6332 17.5803C11.5453 17.7926 11.5 18.0202 11.5 18.25C11.5 18.4798 11.5453 18.7074 11.6332 18.9197C11.7212 19.132 11.8501 19.3249 12.0126 19.4874C12.1751 19.6499 12.368 19.7788 12.5803 19.8668C12.7926 19.9547 13.0202 20 13.25 20C13.5808 19.9996 13.9047 19.9055 14.1842 19.7285C14.4637 19.5515 14.6873 19.2989 14.8291 19H15.75C17.7279 19 19.3569 17.4464 19.4873 15.5H19.5C20.6045 15.5 21.5 14.6045 21.5 13.5V12.5C21.5 11.3955 20.6045 10.5 19.5 10.5V10.25C19.5 9.52117 19.3545 8.87372 19.209 8.4082C19.5002 8.24603 19.7333 7.99364 19.8701 7.68359C20.0491 7.27709 20.0426 6.81452 19.8516 6.41602C18.5096 3.60902 15.7385 2 12.25 2ZM9.5 12C9.23478 12 8.98043 12.1054 8.79289 12.2929C8.60536 12.4804 8.5 12.7348 8.5 13C8.5 13.2652 8.60536 13.5196 8.79289 13.7071C8.98043 13.8946 9.23478 14 9.5 14C9.76522 14 10.0196 13.8946 10.2071 13.7071C10.3946 13.5196 10.5 13.2652 10.5 13C10.5 12.7348 10.3946 12.4804 10.2071 12.2929C10.0196 12.1054 9.76522 12 9.5 12ZM14.5 12C14.2388 12.0006 13.9882 12.1033 13.8017 12.2863C13.6153 12.4692 13.5078 12.7178 13.5024 12.979C13.4969 13.2401 13.5938 13.4931 13.7724 13.6837C13.951 13.8743 14.1971 13.9875 14.458 13.999C14.472 13.9996 14.486 14 14.5 14C14.7652 14 15.0196 13.8946 15.2071 13.7071C15.3946 13.5196 15.5 13.2652 15.5 13C15.5 12.7348 15.3946 12.4804 15.2071 12.2929C15.0196 12.1054 14.7652 12 14.5 12Z" fill="#EB6309" />
                                    </svg>
                                ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <g opacity="0.6">
                                            <path d="M12.25 2C9.86133 2 7.90091 2.6719 6.54688 3.9541C5.19284 5.2363 4.5 7.09191 4.5 9.25V11.1289C3.92989 11.5841 3.5 12.2153 3.5 13C3.5 14.1809 4.35224 15.135 5.45996 15.3906C5.90822 17.1682 6.65342 18.7516 7.70312 19.9141C8.85422 21.1888 10.3534 22 12 22C12.0994 22.0014 12.1981 21.983 12.2903 21.946C12.3825 21.9089 12.4664 21.8539 12.5372 21.7841C12.608 21.7143 12.6642 21.6312 12.7026 21.5395C12.7409 21.4478 12.7607 21.3494 12.7607 21.25C12.7607 21.1506 12.7409 21.0522 12.7026 20.9605C12.6642 20.8688 12.608 20.7857 12.5372 20.7159C12.4664 20.6461 12.3825 20.5911 12.2903 20.554C12.1981 20.517 12.0994 20.4986 12 20.5C10.8411 20.5 9.75106 19.9432 8.81641 18.9082C7.88175 17.8732 7.13641 16.3643 6.76465 14.5928C6.72921 14.4243 6.63688 14.2731 6.50316 14.1646C6.36943 14.0561 6.20246 13.997 6.03027 13.9971C5.87692 13.9971 5.86764 14 6 14C5.43883 14 5 13.5612 5 13C5 12.5982 5.23075 12.2708 5.57129 12.1094C5.69956 12.0486 5.80793 11.9526 5.88382 11.8326C5.95971 11.7126 6 11.5736 6 11.4316V9.25C6 7.40409 6.55716 6.00977 7.57812 5.04297C8.59909 4.07617 10.1387 3.5 12.25 3.5C15.4086 3.5 17.4862 4.94812 18.498 7.06348C18.5078 7.08435 18.5061 7.09123 18.4688 7.10254C17.9156 7.26983 16.5779 7.5 15 7.5C13.0938 7.5 11.8258 7.1448 11.0459 6.81055C10.266 6.4763 10.0303 6.21973 10.0303 6.21973C9.94124 6.13069 9.83123 6.0655 9.71038 6.03017C9.58953 5.99483 9.46173 5.99049 9.33876 6.01753C9.21579 6.04458 9.10161 6.10215 9.00673 6.18493C8.91186 6.26771 8.83936 6.37304 8.7959 6.49121C8.7959 6.49121 8.62875 7.01095 7.79199 7.65527C7.71389 7.71542 7.6484 7.79036 7.59926 7.87582C7.55013 7.96127 7.5183 8.05557 7.5056 8.15333C7.49291 8.25108 7.49959 8.35038 7.52527 8.44555C7.55095 8.54072 7.59513 8.62991 7.65527 8.70801C7.71542 8.78611 7.79036 8.8516 7.87582 8.90074C7.96127 8.94987 8.05557 8.9817 8.15333 8.9944C8.25108 9.00709 8.35038 9.00041 8.44555 8.97473C8.54072 8.94905 8.62991 8.90487 8.70801 8.84473C9.19883 8.46678 9.49946 8.08119 9.73633 7.75C9.98577 7.92275 9.98507 7.98844 10.4541 8.18945C11.4242 8.6052 12.9062 9 15 9C16.0729 9 16.996 8.9089 17.749 8.78516C17.8665 9.13756 18 9.65278 18 10.25V15.25C18 16.5017 17.0017 17.5 15.75 17.5H14.8301C14.6882 17.201 14.4645 16.9483 14.1848 16.7713C13.9051 16.5943 13.581 16.5002 13.25 16.5C13.0202 16.5 12.7926 16.5453 12.5803 16.6332C12.368 16.7212 12.1751 16.8501 12.0126 17.0126C11.8501 17.1751 11.7212 17.368 11.6332 17.5803C11.5453 17.7926 11.5 18.0202 11.5 18.25C11.5 18.4798 11.5453 18.7074 11.6332 18.9197C11.7212 19.132 11.8501 19.3249 12.0126 19.4874C12.1751 19.6499 12.368 19.7788 12.5803 19.8668C12.7926 19.9547 13.0202 20 13.25 20C13.5808 19.9996 13.9047 19.9055 14.1842 19.7285C14.4637 19.5515 14.6873 19.2989 14.8291 19H15.75C17.7279 19 19.3569 17.4464 19.4873 15.5H19.5C20.6045 15.5 21.5 14.6045 21.5 13.5V12.5C21.5 11.3955 20.6045 10.5 19.5 10.5V10.25C19.5 9.52039 19.3545 8.87372 19.209 8.4082C19.8838 8.02462 20.2036 7.15352 19.8516 6.41602C18.5994 3.79837 15.8864 2 12.25 2ZM9.5 12C9.23478 12 8.98043 12.1054 8.79289 12.2929C8.60536 12.4804 8.5 12.7348 8.5 13C8.5 13.2652 8.60536 13.5196 8.79289 13.7071C8.98043 13.8946 9.23478 14 9.5 14C9.76522 14 10.0196 13.8946 10.2071 13.7071C10.3946 13.5196 10.5 13.2652 10.5 13C10.5 12.7348 10.3946 12.4804 10.2071 12.2929C10.0196 12.1054 9.76522 12 9.5 12ZM14.5 12C14.2388 12.0006 13.9882 12.1033 13.8017 12.2863C13.6153 12.4692 13.5078 12.7178 13.5024 12.979C13.4969 13.2401 13.5938 13.4931 13.7724 13.6837C13.951 13.8743 14.1971 13.9875 14.458 13.999C14.472 13.9996 14.486 14 14.5 14C14.7652 14 15.0196 13.8946 15.2071 13.7071C15.3946 13.5196 15.5 13.2652 15.5 13C15.5 12.7348 15.3946 12.4804 15.2071 12.2929C15.0196 12.1054 14.7652 12 14.5 12Z" fill="#EB6309" />
                                    </svg>
                                ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <g opacity="0.6">
                                            <path d="M12.25 2C9.86133 2 7.90091 2.6719 6.54688 3.9541C5.19284 5.2363 4.5 7.09191 4.5 9.25V11.1289C3.92989 11.5841 3.5 12.2153 3.5 13C3.5 14.1809 4.35224 15.135 5.45996 15.3906C5.90822 17.1682 6.65342 18.7516 7.70312 19.9141C8.85422 21.1888 10.3534 22 12 22C12.0994 22.0014 12.1981 21.983 12.2903 21.946C12.3825 21.9089 12.4664 21.8539 12.5372 21.7841C12.608 21.7143 12.6642 21.6312 12.7026 21.5395C12.7409 21.4478 12.7607 21.3494 12.7607 21.25C12.7607 21.1506 12.7409 21.0522 12.7026 20.9605C12.6642 20.8688 12.608 20.7857 12.5372 20.7159C12.4664 20.6461 12.3825 20.5911 12.2903 20.554C12.1981 20.517 12.0994 20.4986 12 20.5C10.8411 20.5 9.75106 19.9432 8.81641 18.9082C7.88175 17.8732 7.13641 16.3643 6.76465 14.5928C6.72921 14.4243 6.63688 14.2731 6.50316 14.1646C6.36943 14.0561 6.20246 13.997 6.03027 13.9971C5.87692 13.9971 5.86764 14 6 14C5.43883 14 5 13.5612 5 13C5 12.5982 5.23075 12.2708 5.57129 12.1094C5.69956 12.0486 5.80793 11.9526 5.88382 11.8326C5.95971 11.7126 6 11.5736 6 11.4316V9.25C6 7.40409 6.55716 6.00977 7.57812 5.04297C8.59909 4.07617 10.1387 3.5 12.25 3.5C15.4086 3.5 17.4862 4.94812 18.498 7.06348C18.5078 7.08435 18.5061 7.09123 18.4688 7.10254C17.9156 7.26983 16.5779 7.5 15 7.5C13.0938 7.5 11.8258 7.1448 11.0459 6.81055C10.266 6.4763 10.0303 6.21973 10.0303 6.21973C9.94124 6.13069 9.83123 6.0655 9.71038 6.03017C9.58953 5.99483 9.46173 5.99049 9.33876 6.01753C9.21579 6.04458 9.10161 6.10215 9.00673 6.18493C8.91186 6.26771 8.83936 6.37304 8.7959 6.49121C8.7959 6.49121 8.62875 7.01095 7.79199 7.65527C7.71389 7.71542 7.6484 7.79036 7.59926 7.87582C7.55013 7.96127 7.5183 8.05557 7.5056 8.15333C7.49291 8.25108 7.49959 8.35038 7.52527 8.44555C7.55095 8.54072 7.59513 8.62991 7.65527 8.70801C7.71542 8.78611 7.79036 8.8516 7.87582 8.90074C7.96127 8.94987 8.05557 8.9817 8.15333 8.9944C8.25108 9.00709 8.35038 9.00041 8.44555 8.97473C8.54072 8.94905 8.62991 8.90487 8.70801 8.84473C9.19883 8.46678 9.49946 8.08119 9.73633 7.75C9.98577 7.92275 9.98507 7.98844 10.4541 8.18945C11.4242 8.6052 12.9062 9 15 9C16.0729 9 16.996 8.9089 17.749 8.78516C17.8665 9.13756 18 9.65278 18 10.25V15.25C18 16.5017 17.0017 17.5 15.75 17.5H14.8301C14.6882 17.201 14.4645 16.9483 14.1848 16.7713C13.9051 16.5943 13.581 16.5002 13.25 16.5C13.0202 16.5 12.7926 16.5453 12.5803 16.6332C12.368 16.7212 12.1751 16.8501 12.0126 17.0126C11.8501 17.1751 11.7212 17.368 11.6332 17.5803C11.5453 17.7926 11.5 18.0202 11.5 18.25C11.5 18.4798 11.5453 18.7074 11.6332 18.9197C11.7212 19.132 11.8501 19.3249 12.0126 19.4874C12.1751 19.6499 12.368 19.7788 12.5803 19.8668C12.7926 19.9547 13.0202 20 13.25 20C13.5808 19.9996 13.9047 19.9055 14.1842 19.7285C14.4637 19.5515 14.6873 19.2989 14.8291 19H15.75C17.7279 19 19.3569 17.4464 19.4873 15.5H19.5C20.6045 15.5 21.5 14.6045 21.5 13.5V12.5C21.5 11.3955 20.6045 10.5 19.5 10.5V10.25C19.5 9.52117 19.3545 8.87372 19.209 8.4082C19.5002 8.24603 19.7333 7.99364 19.8701 7.68359C20.0491 7.27709 20.0426 6.81452 19.8516 6.41602C18.5096 3.60902 15.7385 2 12.25 2ZM9.5 12C9.23478 12 8.98043 12.1054 8.79289 12.2929C8.60536 12.4804 8.5 12.7348 8.5 13C8.5 13.2652 8.60536 13.5196 8.79289 13.7071C8.98043 13.8946 9.23478 14 9.5 14C9.76522 14 10.0196 13.8946 10.2071 13.7071C10.3946 13.5196 10.5 13.2652 10.5 13C10.5 12.7348 10.3946 12.4804 10.2071 12.2929C10.0196 12.1054 9.76522 12 9.5 12ZM14.5 12C14.2388 12.0006 13.9882 12.1033 13.8017 12.2863C13.6153 12.4692 13.5078 12.7178 13.5024 12.979C13.4969 13.2401 13.5938 13.4931 13.7724 13.6837C13.951 13.8743 14.1971 13.9875 14.458 13.999C14.472 13.9996 14.486 14 14.5 14C14.7652 14 15.0196 13.8946 15.2071 13.7071C15.3946 13.5196 15.5 13.2652 15.5 13C15.5 12.7348 15.3946 12.4804 15.2071 12.2929C15.0196 12.1054 14.7652 12 14.5 12Z" fill="#EB6309" />
                                    </svg>
                                ) : (
                                    <svg xmlns="http://www.w3.org/2000/svg" width="24" height="24" viewBox="0 0 24 24" fill="none">
                                        <g opacity="0.6">
                                            <path d="M12.25 2C9.86133 2 7.90091 2.6719 6.54688 3.9541C5.19284 5.2363 4.5 7.09191 4.5 9.25V11.1289C3.92989 11.5841 3.5 12.2153 3.5 13C3.5 14.1809 4.35224 15.135 5.45996 15.3906C5.90822 17.1682 6.65342 18.7516 7.70312 19.9141C8.85422 21.1888 10.3534 22 12 22C12.0994 22.0014 12.1981 21.983 12.2903 21.946C12.3825 21.9089 12.4664 21.8539 12.5372 21.7841C12.608 21.7143 12.6642 21.6312 12.7026 21.5395C12.7409 21.4478 12.7607 21.3494 12.7607 21.25C12.7607 21.1506 12.7409 21.0522 12.7026 20.9605C12.6642 20.8688 12.608 20.7857 12.5372 20.7159C12.4664 20.6461 12.3825 20.5911 12.2903 20.554C12.1981 20.517 12.0994 20.4986 12 20.5C10.8411 20.5 9.75106 19.9432 8.81641 18.9082C7.88175 17.8732 7.13641 16.3643 6.76465 14.5928C6.72921 14.4243 6.63688 14.2731 6.50316 14.1646C6.36943 14.0561 6.20246 13.997 6.03027 13.9971C5.87692 13.9971 5.86764 14 6 14C5.43883 14 5 13.5612 5 13C5 12.5982 5.23075 12.2708 5.57129 12.1094C5.69956 12.0486 5.80793 11.9526 5.88382 11.8326C5.95971 11.7126 6 11.5736 6 11.4316V9.25C6 7.40409 6.55716 6.00977 7.57812 5.04297C8.59909 4.07617 10.1387 3.5 12.25 3.5C15.4086 3.5 17.4862 4.94812 18.498 7.06348C18.5078 7.08435 18.5061 7.09123 18.4688 7.10254C17.9156 7.26983 16.5779 7.5 15 7.5C13.0938 7.5 11.8258 7.1448 11.0459 6.81055C10.266 6.4763 10.0303 6.21973 10.0303 6.21973C9.94124 6.13069 9.83123 6.0655 9.71038 6.03017C9.58953 5.99483 9.46173 5.99049 9.33876 6.01753C9.21579 6.04458 9.10161 6.10215 9.00673 6.18493C8.91186 6.26771 8.83936 6.37304 8.7959 6.49121C8.7959 6.49121 8.62875 7.01095 7.79199 7.65527C7.71389 7.71542 7.6484 7.79036 7.59926 7.87582C7.55013 7.96127 7.5183 8.05557 7.5056 8.15333C7.49291 8.25108 7.49959 8.35038 7.52527 8.44555C7.55095 8.54072 7.59513 8.62991 7.65527 8.70801C7.71542 8.78611 7.79036 8.8516 7.87582 8.90074C7.96127 8.94987 8.05557 8.9817 8.15333 8.9944C8.25108 9.00709 8.35038 9.00041 8.44555 8.97473C8.54072 8.94905 8.62991 8.90487 8.70801 8.84473C9.19883 8.46678 9.49946 8.08119 9.73633 7.75C9.98577 7.92275 9.98507 7.98844 10.4541 8.18945C11.4242 8.6052 12.9062 9 15 9C16.0729 9 16.996 8.9089 17.749 8.78516C17.8665 9.13756 18 9.65278 18 10.25V15.25C18 16.5017 17.0017 17.5 15.75 17.5H14.8301C14.6882 17.201 14.4645 16.9483 14.1848 16.7713C13.9051 16.5943 13.581 16.5002 13.25 16.5C13.0202 16.5 12.7926 16.5453 12.5803 16.6332C12.368 16.7212 12.1751 16.8501 12.0126 17.0126C11.8501 17.1751 11.7212 17.368 11.6332 17.5803C11.5453 17.7926 11.5 18.0202 11.5 18.25C11.5 18.4798 11.5453 18.7074 11.6332 18.9197C11.7212 19.132 11.8501 19.3249 12.0126 19.4874C12.1751 19.6499 12.368 19.7788 12.5803 19.8668C12.7926 19.9547 13.0202 20 13.25 20C13.5808 19.9996 13.9047 19.9055 14.1842 19.7285C14.4637 19.5515 14.6873 19.2989 14.8291 19H15.75C17.7279 19 19.3569 17.4464 19.4873 15.5H19.5C20.6045 15.5 21.5 14.6045 21.5 13.5V12.5C21.5 11.3955 20.6045 10.5 19.5 10.5V10.25C19.5 9.52039 19.3545 8.87285 19.209 8.40723C19.5002 8.24603 19.7333 7.99364 19.8701 7.68359C20.0491 7.27709 20.0426 6.81452 19.8516 6.41602C18.5096 3.60902 15.7385 2 12.25 2ZM9.5 12C9.23478 12 8.98043 12.1054 8.79289 12.2929C8.60536 12.4804 8.5 12.7348 8.5 13C8.5 13.2652 8.60536 13.5196 8.79289 13.7071C8.98043 13.8946 9.23478 14 9.5 14C9.76522 14 10.0196 13.8946 10.2071 13.7071C10.3946 13.5196 10.5 13.2652 10.5 13C10.5 12.7348 10.3946 12.4804 10.2071 12.2929C10.0196 12.1054 9.76522 12 9.5 12ZM14.5 12C14.2388 12.0006 13.9882 12.1033 13.8017 12.2863C13.6153 12.4692 13.5078 12.7178 13.5024 12.979C13.4969 13.2401 13.5938 13.4931 13.7724 13.6837C13.951 13.8743 14.1971 13.9875 14.458 13.999C14.472 13.9996 14.486 14 14.5 14C14.7652 14 15.0196 13.8946 15.2071 13.7071C15.3946 13.5196 15.5 13.2652 15.5 13C15.5 12.7348 15.3946 12.4804 15.2071 12.2929C15.0196 12.1054 14.7652 12 14.5 12Z" fill="#999999" />
                                        </g>
                                    </svg>
                                )}
                                {isOpen && <span className="text-gray-700 max-md:hidden">Support</span>}
                            </div> */}
            </div>
          </div>
          <div className={`${isOpen ? "p-5" : "p-2"} max-md:p-2`}>
            {/* Logout Option */}
            <div
              onClick={handleLogout}
              className="flex items-center gap-3 p-3 rounded-full cursor-pointer transition-all hover:bg-[#FFE5D4] hover:text-orange-500"
            >
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M15 17.625C14.9264 19.4769 13.3831 21.0494 11.3156 20.9988C10.8346 20.987 10.2401 20.8194 9.05112 20.484C6.18961 19.6768 3.70555 18.3203 3.10956 15.2815C3 14.723 3 14.0944 3 12.8373V11.1627C3 9.90561 3 9.27705 3.10956 8.71846C3.70555 5.67965 6.18961 4.32316 9.05112 3.51603C10.2401 3.18064 10.8346 3.01295 11.3156 3.00119C13.3831 2.95061 14.9264 4.52307 15 6.37501"
                  stroke="#DA1E28"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                />
                <path
                  d="M21 12H10M21 12C21 11.2998 19.0057 9.99153 18.5 9.5M21 12C21 12.7002 19.0057 14.0085 18.5 14.5"
                  stroke="#DA1E28"
                  strokeWidth="1.5"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                />
              </svg>
              {isOpen && (
                <span className="text-gray-700 max-md:hidden">Logout</span>
              )}
            </div>
          </div>
        </div>
        <div
          className={`${isOpen ? "" : "hidden"} max-md:hidden text-xs p-2 text-center`}
        >
          © 2005-2025 Graphy, Inc. All rights reserved.
        </div>
      </div>
    </div>
  );
}
