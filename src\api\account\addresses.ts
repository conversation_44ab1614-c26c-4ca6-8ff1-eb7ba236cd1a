import { API_ROUTES } from "@/utils/ApiRoutes";
import {
  AddAddressPayload,
  DeleteAddressPayload,
  UpdateAddressPayload,
  ApiResponse,
  DoctorAddressApiResponse,
} from "@/types/types";
import { toast } from "react-toastify";
import { getDecryptedToken } from "@/app/lib/auth";

export interface Doctor<PERSON>dd<PERSON> {
  id: string;
  doctor_id: string;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  created_at: string;
  updated_at: string;
  address_type: "ship_to" | "bill_to" | string;
}

// ✅ Fetch Addresses
export async function fetchAddresses(): Promise<DoctorAddress[] | null> {
  const token = getDecryptedToken("AccessToken");
  const url = API_ROUTES.ADRESSES.GET_ADRESS;
  try {
    const response = await fetch(url, {
      headers: {
        accept: "application/json",
        Authorization: `Bearer ${token}`,
      },
    });

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: DoctorAddressApiResponse = await response.json();

    if (data.success) {
      return data.data as unknown as <PERSON><PERSON>ddress[];
    } else {
      return [];
    }
  } catch (error) {
    console.log("🚀 ~ fetchAddresses ~ error:", error);
    return [];
  }
}

// ✅ Add Address
export const addAddress = async (
  payload: AddAddressPayload
): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(API_ROUTES.ADRESSES.ADD_ADDRESS, {
      method: "POST",
      headers: {
        Accept: "application/json",
        Authorization: `Bearer ${payload.token}`,
      },
      body: formData,
      credentials: "include",
    });

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      toast.error(data?.message);
    } else {
      toast.success(data?.message);
    }
    return data;
  } catch {
    throw null;
  }
};

// ✅ Delete Address
export const deleteAddress = async (
  payload: DeleteAddressPayload
): Promise<ApiResponse> => {
  try {
    const res = await fetch(
      `${API_ROUTES.ADRESSES.DELETE_ADDRESS}/${payload.addressId}`,
      {
        method: "DELETE",
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${payload.token}`,
        },
        credentials: "include",
      }
    );

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      toast.error(data?.message || "Failed to delete address");
    } else {
      toast.success(data?.message || "Address deleted successfully");
    }
    return data;
  } catch {
    throw null;
  }
};

// ✅ Update Address
export const updateAddress = async (
  payload: UpdateAddressPayload
): Promise<ApiResponse> => {
  try {
    const formData = new FormData();
    formData.append("clinic_name", payload.clinic_name);
    formData.append("street_address", payload.street_address);
    formData.append("city", payload.city);
    formData.append("postal_code", payload.postal_code);
    formData.append("phone_number", payload.phone_number);
    formData.append("address_type", payload.address_type);

    const res = await fetch(
      `${API_ROUTES.ADRESSES.UPDATE_ADDRESS}/${payload.addressId}`,
      {
        method: "PUT",
        headers: {
          Accept: "application/json",
          Authorization: `Bearer ${payload.token}`,
        },
        body: formData,
        credentials: "include",
      }
    );

    const data: ApiResponse = await res.json();
    if (!res.ok) {
      toast.error(data?.message || "Failed to update address");
    } else {
      toast.success(data?.message || "Address updated successfully");
    }
    return data;
  } catch {
    throw null;
  }
};
