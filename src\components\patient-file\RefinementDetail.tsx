"use client";
import { zodResolver } from "@hookform/resolvers/zod";
import { FieldErrors, useForm } from "react-hook-form";
import { toast } from "react-toastify";
import { z } from "zod";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { TabName } from "./RefinementModel";
import RoundCheckboxRegister from "../reuseable/RoundCheckBoxRegister";

export const detailsSchema = z
  .object({
    isTreatmentComplete: z.string({
      required_error: "Please provide treatment status",
    }),
    lastUpperTrackingNumber: z
      .number({ required_error: "Upper tracking number is required" })
      .int()
      .min(0, { message: "Number cannot be negative" })
      .optional(),
    lastLowerTrackingNumber: z
      .number({ required_error: "Lower tracking number is required" })
      .int()
      .min(0, { message: "Number cannot be negative" })
      .optional(),
    reason: z.object({
      reasons: z.array(z.string()),
      otherReason: z.string().optional(),
    }),
    note: z.string({ required_error: "Please write a note" }).optional(),
  })
  .superRefine((data, ctx) => {
    if (data.isTreatmentComplete == "no" && !data.lastUpperTrackingNumber) {
      ctx.addIssue({
        path: ["lastUpperTrackingNumber"],
        code: z.ZodIssueCode.custom,
        message: "Upper tracking number is required",
      });
    }
    if (data.reason.reasons.length < 1) {
      ctx.addIssue({
        path: ["reason"],
        code: z.ZodIssueCode.custom,
        message: "Reason is required",
      });
    }
    if (data.reason.reasons.includes("other") && !data.reason.otherReason) {
      ctx.addIssue({
        path: ["reason.otherReason"],
        code: z.ZodIssueCode.custom,
        message: "Other reason is required",
      });
    }
    if (data.isTreatmentComplete == "no" && !data.lastLowerTrackingNumber) {
      ctx.addIssue({
        path: ["lastLowerTrackingNumber"],
        code: z.ZodIssueCode.custom,
        message: "Lower tracking number is required",
      });
    }
  });

type RefinementDetailsSchema = z.infer<typeof detailsSchema>;

interface props {
  setTab: (val: TabName, force: boolean) => void;
  onClose: () => void;
  setDetailsData: React.Dispatch<
    React.SetStateAction<{
      isTreatmentComplete: string;
      lastUpperTrackingNumber?: number;
      lastLowerTrackingNumber?: number;
      reason: {
        reasons: string[];
        otherReason?: string;
      };
      note?: string;
    }>
  >;
}

const RefinementDetails: React.FC<props> = ({
  setTab,
  onClose,
  setDetailsData,
}) => {
  const {
    register,
    watch,
    handleSubmit,
    formState: { errors },
  } = useForm<RefinementDetailsSchema>({
    resolver: zodResolver(detailsSchema),
    defaultValues: {
      note: "",
      isTreatmentComplete: "yes",
      lastLowerTrackingNumber: 0,
      lastUpperTrackingNumber: 0,
      reason: { reasons: [], otherReason: "" },
    },
    mode: "onChange",
  });

  const option = watch("isTreatmentComplete");

  const onSubmit = (data: RefinementDetailsSchema) => {
    console.log("Details data:", data);
    setDetailsData(data);
    setTab("scan", true);
  };

  const onError = (errors: FieldErrors<RefinementDetailsSchema>) => {
    toast.error("Please fill all the required fields");
    console.log("❌ Form validation errors:", errors);
  };

  return (
    <div>
      <form
        className="flex-grow flex flex-col justify-between"
        onSubmit={handleSubmit(onSubmit, onError)}
      >
        <div className="my-4">
          <div className="flex flex-col gap-3">
            <label className="" htmlFor="">
              Is Treatment Complete
            </label>

            <div className="flex flex-col gap-1">
              <RoundRadioButton
                register={register}
                value="yes"
                name="isTreatmentComplete"
                label="Yes"
                id="upper-details"
                labelClass="!text-dark text-base"
              />
              <RoundRadioButton
                register={register}
                value="no"
                name="isTreatmentComplete"
                label="No"
                id="lower-details"
                labelClass="!text-dark text-base"
              />
              {errors.isTreatmentComplete?.message && (
                <p className="text-sm text-danger">
                  {errors.isTreatmentComplete.message}
                </p>
              )}
            </div>
          </div>
          {option == "no" && (
            <div className="gap-3 mt-2 grid grid-cols-2">
              <div className="flex flex-col gap-1 col-span-1">
                <label htmlFor="">Last upper tracking #</label>
                <input
                  {...register("lastUpperTrackingNumber", {
                    valueAsNumber: true,
                  })}
                  className="border border-neutral-400 rounded-lg px-2 py-3 w-full"
                  min={0}
                  type="number"
                />
                {errors.lastUpperTrackingNumber?.message && (
                  <p className="text-sm text-danger">
                    {errors.lastUpperTrackingNumber?.message}
                  </p>
                )}
              </div>
              <div className="flex flex-col gap-1 col-span-1">
                <label htmlFor="">Last lower tracking #</label>
                <input
                  {...register("lastLowerTrackingNumber", {
                    valueAsNumber: true,
                  })}
                  className="border border-neutral-400 rounded-lg px-2 py-3 w-full"
                  min={0}
                  type="number"
                />
                {errors.lastLowerTrackingNumber?.message && (
                  <p className="text-sm text-danger">
                    {errors.lastLowerTrackingNumber.message}
                  </p>
                )}
              </div>
            </div>
          )}
        </div>

        <div className="flex flex-col gap-1 mb-2">
          <label className="text-dark font-semibold" htmlFor="note-replacement">
            Reason for Refinement:
          </label>
          <div className="flex flex-col gap-3 ps-4">
            <RoundCheckboxRegister
              label="Phased treatment requires refinement"
              value="Phased treatment requires refinement"
              name="reason-refinement"
              id="reason-refinement-file"
              register={register("reason.reasons")}
            />
            <RoundCheckboxRegister
              label="Teeth movement deviates from treatment plan"
              value="Teeth movement deviates from treatment plan"
              name="reason-refinement-1"
              id="reason-refinement-file-1"
              register={register("reason.reasons")}
            />
            <RoundCheckboxRegister
              label="Treatment plan changed (Please describe in detail in the Special Notes section below)"
              value="Treatment plan changed (Please describe in detail in the Special Notes section below)"
              name="reason-refinement"
              id="reason-refinement-file-2"
              register={register("reason.reasons")}
            />
            <RoundCheckboxRegister
              label="The patient has a new restoration or filling"
              value="The patient has a new restoration or filling"
              name="reason-refinement"
              id="reason-refinement-file-3"
              register={register("reason.reasons")}
            />
            <RoundCheckboxRegister
              label="Poor patient compliance"
              value="Poor patient compliance"
              name="reason-refinement"
              id="reason-refinement-file-4"
              register={register("reason.reasons")}
            />
            <RoundCheckboxRegister
              label="Others (Please leave remarks)"
              value="other"
              name="reason-refinement"
              id="reason-refinement-file-5"
              register={register("reason.reasons")}
            />
            {errors.reason?.message && (
              <p className="text-sm text-danger">{errors.reason.message}</p>
            )}
            {watch("reason.reasons").includes("other") && (
              <textarea
                {...register("reason.otherReason")}
                className="w-full px-2 py-2 rounded-md border border-neutral-300"
                placeholder="Type here..."
                id="note-replacement"
              ></textarea>
            )}
            {errors.reason?.otherReason?.message && (
              <p className="text-sm text-danger">
                {errors.reason?.otherReason?.message}
              </p>
            )}
          </div>
        </div>
        <div className="flex flex-col gap-1">
          <label className="text-dark font-semibold" htmlFor="note-replacement">
            Additional notes:
          </label>
          <textarea
            {...register("note")}
            className="w-full px-2 py-2 rounded-md border border-neutral-300"
            placeholder="Type here..."
            id="note-replacement"
          ></textarea>
        </div>

        <div className="flex items-center justify-end gap-3 my-4">
          <button
            onClick={() => onClose()}
            className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray min-w-[120px]"
            type="button"
          >
            <span className="font-semibold text-lg text-dark">Cancel</span>
          </button>
          <button
            className="flex items-center justify-center py-2.5 cursor-pointer rounded-full bg-primary min-w-48 hover:bg-[#D45A08] transition"
            type="submit"
          >
            <span className="font-semibold text-base text-white">Next</span>
          </button>
        </div>
      </form>
    </div>
  );
};

export default RefinementDetails;
