"use client"

import type React from "react"
import { useState } from "react"
import { toast } from "react-toastify"
import type { Address, ProfileData } from "@/types/types"
import { getDecryptedToken, setEncryptedToken } from "@/app/lib/auth" // Added setEncryptedToken import
import { addAddress, deleteAddress, updateAddress } from "@/api/account/addresses"
import AddressModal from "./modals/AddressModal"
import DeleteConfirmationModal from "@/components/reuseable/DeleteConfirmationModal"
import Image from "next/image"
import { useRouter } from "next/navigation"
import { updateDoctorProfile } from "@/api/account/profile" // Add this import
import { changePassword } from "@/utils/ApisHelperFunction"

type ModalAddress = {
  id?: string | number
  clinic_name: string
  street_address: string
  city: string
  postal_code: string
  phone_number: string
  address_type: "ship_to" | "bill_to"
}

const initialModalAddress: ModalAddress = {
  id: "",
  clinic_name: "",
  street_address: "",
  city: "",
  postal_code: "",
  phone_number: "",
  address_type: "ship_to",
}

interface DrProfileProps {
  data: Address[]
  Profiledata: ProfileData
}

const validatePassword = (password: string) => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[0-9]/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    errors.push("Password must contain at least one special character")
  }

  return errors
}

const DrProfile: React.FC<DrProfileProps> = ({ data, Profiledata }) => {
  // Split addresses by type directly from props
  const shippingAddresses = data.filter((addr) => addr.address_type === "ship_to")
  const billingAddresses = data.filter((addr) => addr.address_type === "bill_to")

  const [username, setUsername] = useState(Profiledata?.username || "")
  const [firstName, setFirstName] = useState(Profiledata?.first_name || "")
  const [lastName, setLastName] = useState(Profiledata?.last_name || "")
  const [email, setEmail] = useState(Profiledata?.email || "")
  const [currentProfileImage, setCurrentProfileImage] = useState(Profiledata?.profile_image || "")

  // Profile fields from props
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>("")
  const [isEditingUsername, setIsEditingUsername] = useState(false)
  const [isEditingFirstName, setIsEditingFirstName] = useState(false)
  const [isEditingLastName, setIsEditingLastName] = useState(false)
  const [isEditingEmail, setIsEditingEmail] = useState(false)
  const [newUsername, setNewUsername] = useState(Profiledata?.username || "")
  const [newFirstName, setNewFirstName] = useState(Profiledata?.first_name || "")
  const [newLastName, setNewLastName] = useState(Profiledata?.last_name || "")
  const [newEmail, setNewEmail] = useState(Profiledata?.email || "")
  const [updateLoading, setUpdateLoading] = useState(false)
  const [password] = useState("*************") // Added password state variable to fix TypeScript error

  // Notification preferences
  const [notifications, setNotifications] = useState(true)
  const [isEditingNotifications, setIsEditingNotifications] = useState(false)

  // Address Modal State
  const [addressModalOpen, setAddressModalOpen] = useState(false)
  const [addressModalType, setAddressModalType] = useState<"add" | "edit" | null>(null)
  const [addressType, setAddressType] = useState<"ship_to" | "bill_to">("ship_to")
  const [addressModalData, setAddressModalData] = useState<ModalAddress>({ ...initialModalAddress })

  const [confirmModalOpen, setConfirmModalOpen] = useState(false)
  const [addressToDelete, setAddressToDelete] = useState<{ type: "shipping" | "billing"; id: string } | null>(null)

  // Default address IDs
  const [defaultShippingId, setDefaultShippingId] = useState<number | null>(shippingAddresses[0]?.id ?? null)
  const [defaultBillingId, setDefaultBillingId] = useState<number | null>(billingAddresses[0]?.id ?? null)
  const [isEditingPassword, setIsEditingPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  const [newPasswordErrors, setNewPasswordErrors] = useState<string[]>([])
  const [confirmPasswordErrors, setConfirmPasswordErrors] = useState<string[]>([])
  const [passwordsMatch, setPasswordsMatch] = useState(true)
  const [passwordSameAsOld, setPasswordSameAsOld] = useState(false)
  const router = useRouter()

  const handleChangeField = (
    isEditing: boolean,
    setEditing: React.Dispatch<React.SetStateAction<boolean>>,
    value: string,
    setValue: React.Dispatch<React.SetStateAction<string>>,
    newValue: string,
    setNewValue: React.Dispatch<React.SetStateAction<string>>,
  ) => {
    if (isEditing) {
      if (newValue.trim()) setValue(newValue)
      setEditing(false)
    } else {
      setNewValue(value)
      setEditing(true)
    }
  }

  // Handle image upload
  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
      if (!validTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPEG, PNG, GIF, WebP)")
        return
      }
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB")
        return
      }
      setSelectedImage(file)
      const reader = new FileReader()
      reader.onload = (event) => {
        const newImageUrl = event.target?.result as string
        setImagePreview(newImageUrl)
        setCurrentProfileImage(newImageUrl)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = () => {
    setSelectedImage(null)
    setImagePreview("")
    setCurrentProfileImage(Profiledata?.profile_image || "")
    const fileInput = document.getElementById("profile-image") as HTMLInputElement
    if (fileInput) fileInput.value = ""
  }

  const handleChangeUsername = () =>
    handleChangeField(isEditingUsername, setIsEditingUsername, username, setUsername, newUsername, setNewUsername)

  const handleChangeFirstName = () =>
    handleChangeField(isEditingFirstName, setIsEditingFirstName, firstName, setFirstName, newFirstName, setNewFirstName)

  const handleChangeLastName = () =>
    handleChangeField(isEditingLastName, setIsEditingLastName, lastName, setLastName, newLastName, setNewLastName)

  const handleChangeEmail = () =>
    handleChangeField(isEditingEmail, setIsEditingEmail, email, setEmail, newEmail, setNewEmail)

  const handleChangeNotifications = () => setIsEditingNotifications((prev) => !prev)
  const toggleNotifications = () => setNotifications((prev) => !prev)

  // Profile update handler (API call should be implemented)
  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    if (!firstName.trim() || !lastName.trim() || !username.trim() || !email.trim()) {
      toast.error("All fields are required.")
      return
    }
    setUpdateLoading(true)
    try {
      const token = getDecryptedToken("AccessToken") || ""
      const payload = {
        first_name: firstName,
        last_name: lastName,
        username: username,
        email: email,
        profileImage: selectedImage || null,
      }
      await updateDoctorProfile(token, payload)

      setEncryptedToken("first_name", firstName)
      setEncryptedToken("last_name", lastName)
      setEncryptedToken("username", username)
      if (selectedImage) {
        const imageUrl = URL.createObjectURL(selectedImage)
        setEncryptedToken("profile_image", imageUrl)
      }

      const profileUpdateEvent = new CustomEvent("profileUpdated", {
        detail: {
          first_name: firstName,
          last_name: lastName,
          username: username,
          email: email,
          profile_image: selectedImage ? URL.createObjectURL(selectedImage) : Profiledata?.profile_image,
        },
      })
      window.dispatchEvent(profileUpdateEvent)

      router.refresh()
    } catch {
      toast.error("Profile update failed!")
    }
    setUpdateLoading(false)
    setIsEditingFirstName(false)
    setIsEditingLastName(false)
    setIsEditingUsername(false)
    setIsEditingEmail(false)
  }

  // Address management handlers
  const handleAddAddress = (type: "ship_to" | "bill_to") => {
    setAddressModalType("add")
    setAddressType(type)
    setAddressModalData({ ...initialModalAddress, address_type: type })
    setAddressModalOpen(true)
  }

  const handleEditAddress = (addr: Address) => {
    setAddressModalType("edit")
    setAddressType(addr.address_type as "ship_to" | "bill_to")
    setAddressModalData({
      id: addr.id,
      clinic_name: addr.clinic_name,
      street_address: addr.street_address,
      city: addr.city,
      postal_code: addr.postal_code,
      phone_number: addr.phone_number,
      address_type: addr.address_type as "ship_to" | "bill_to",
    })
    setAddressModalOpen(true)
  }

  const handleAddressChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    setAddressModalData({
      ...addressModalData,
      [e.target.name]: e.target.value,
    })
  }

  const handleAddressSubmit = async (e: React.FormEvent) => {
    e.preventDefault()
    const token = getDecryptedToken("AccessToken") || ""
    const { id, clinic_name, street_address, city, postal_code, phone_number } = addressModalData

    if (addressModalType === "add") {
      await addAddress({
        clinic_name,
        street_address,
        city,
        postal_code,
        phone_number,
        address_type: addressType,
        token,
      })
    } else if (addressModalType === "edit") {
      await updateAddress({
        addressId: id ?? "",
        clinic_name,
        street_address,
        city,
        postal_code,
        phone_number,
        address_type: addressType,
        token,
      })
    }
    setAddressModalOpen(false)
    router.refresh()
  }

  const setDefaultAddress = (id: number, type: "ship_to" | "bill_to") => {
    if (type === "ship_to") setDefaultShippingId(id)
    else setDefaultBillingId(id)
  }

  const handleDeleteAddress = (type: "shipping" | "billing", id: number) => {
    setAddressToDelete({ type, id: id.toString() })
    setConfirmModalOpen(true)
  }

  const confirmDeleteAddress = async () => {
    if (!addressToDelete) return
    const { id } = addressToDelete
    const token = getDecryptedToken("AccessToken")
    await deleteAddress({ addressId: id, token: token || "" })
    setConfirmModalOpen(false)
    setAddressToDelete(null)
    router.refresh()
  }

  const cancelDeleteAddress = () => {
    setConfirmModalOpen(false)
    setAddressToDelete(null)
  }

  const handleCancelPasswordEdit = () => {
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
    setNewPasswordErrors([])
    setConfirmPasswordErrors([])
    setPasswordsMatch(true)
    setPasswordSameAsOld(false)
    setIsEditingPassword(false)
  }

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setNewPassword(password)
    setNewPasswordErrors(validatePassword(password))

    setPasswordSameAsOld(Boolean(currentPassword && password === currentPassword))

    if (confirmPassword) {
      setPasswordsMatch(password === confirmPassword)
    }
  }

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setConfirmPassword(password)
    setConfirmPasswordErrors(validatePassword(password))
    setPasswordsMatch(newPassword === password)
  }

  const handleChangePassword = async () => {
    if (isEditingPassword) {
      if (!currentPassword.trim() || !newPassword.trim() || !confirmPassword.trim()) {
        toast.error("All password fields are required.")
        return
      }

      if (passwordSameAsOld) {
        return
      }

      if (newPasswordErrors.length > 0 || confirmPasswordErrors.length > 0) {
        toast.error("Please fix password validation errors before submitting.")
        return
      }

      if (!passwordsMatch) {
        toast.error("New password and confirm password do not match.")
        return
      }

      const success = await changePassword(currentPassword, newPassword, confirmPassword)

      if (success) {
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")
        setNewPasswordErrors([])
        setConfirmPasswordErrors([])
        setPasswordsMatch(true)
        setPasswordSameAsOld(false)
        setIsEditingPassword(false)
      }
    } else {
      setIsEditingPassword(true)
    }
  }

  return (
    <>
      <div className="bg-white p-6 rounded-lg shadow-sm">
        <form onSubmit={handleUpdateProfile} className="grid grid-cols-1 md:grid-cols-2 gap-8">
          {/* User Information Section */}
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Profile</h2>
            {/* Profile Image Section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700 mb-2">Profile Image</p>
                <div className="flex items-center justify-between">
                  <div className="w-20 h-20 rounded-full border-2 border-gray-300 overflow-hidden bg-gray-100 flex items-center justify-center">
                    {currentProfileImage ? (
                      <Image
                        src={currentProfileImage || "/placeholder.svg"}
                        alt="Profile"
                        width={1000}
                        height={1000}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    )}
                  </div>
                  <div className="flex items-center gap-2 ">
                    <label
                      htmlFor="profile-image"
                      className="bg-[#EB6309] margin-[0px] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm text-center"
                    >
                      {currentProfileImage ? "Change Image" : "Upload Image"}
                    </label>
                    <input
                      id="profile-image"
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    {(imagePreview || selectedImage) && (
                      <button
                        type="button"
                        onClick={removeImage}
                        className="text-red-500 text-sm hover:text-red-700 transition-colors"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>
            {/* First Name */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div>
                <p className="font-medium text-gray-700">First Name</p>
                {isEditingFirstName ? (
                  <input
                    type="text"
                    value={newFirstName}
                    onChange={(e) => setNewFirstName(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter first name"
                    title="First Name"
                  />
                ) : (
                  <p className="text-gray-600">{firstName}</p>
                )}
              </div>
              <button
                type="button"
                onClick={handleChangeFirstName}
                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
              >
                {isEditingFirstName ? "Save" : "Change"}
              </button>
            </div>
            {/* Last Name */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div>
                <p className="font-medium text-gray-700">Last Name</p>
                {isEditingLastName ? (
                  <input
                    type="text"
                    value={newLastName}
                    onChange={(e) => setNewLastName(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter last name"
                    title="Last Name"
                  />
                ) : (
                  <p className="text-gray-600">{lastName}</p>
                )}
              </div>
              <button
                type="button"
                onClick={handleChangeLastName}
                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
              >
                {isEditingLastName ? "Save" : "Change"}
              </button>
            </div>
            {/* Username */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div>
                <p className="font-medium text-gray-700">Username</p>
                {isEditingUsername ? (
                  <input
                    type="text"
                    value={newUsername}
                    onChange={(e) => setNewUsername(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter username"
                    title="Username"
                  />
                ) : (
                  <p className="text-gray-600">{username}</p>
                )}
              </div>
              <button
                type="button"
                onClick={handleChangeUsername}
                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
              >
                {isEditingUsername ? "Save" : "Change"}
              </button>
            </div>
            {/* Primary Account Email */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div>
                <p className="font-medium text-gray-700">Primary Account Email*</p>
                {isEditingEmail ? (
                  <input
                    type="email"
                    value={newEmail}
                    onChange={(e) => setNewEmail(e.target.value)}
                    className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    placeholder="Enter email"
                    title="Email"
                  />
                ) : (
                  <p className="text-gray-600">{email}</p>
                )}
              </div>
              <button
                type="button"
                onClick={handleChangeEmail}
                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
              >
                {isEditingEmail ? "Save" : "Change"}
              </button>
            </div>
            {/* Password */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Password</p>
                {isEditingPassword ? (
                  <div className="space-y-3 mt-2">
                    <input
                      type="password"
                      placeholder="Current Password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />

                    <div>
                      <input
                        type="password"
                        placeholder="New Password"
                        value={newPassword}
                        onChange={handleNewPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${newPasswordErrors.length > 0 || passwordSameAsOld ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      {passwordSameAsOld && (
                        <p className="text-red-500 text-xs mt-1">
                          New password should be different from current password
                        </p>
                      )}
                      {newPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {newPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>

                    <div>
                      <input
                        type="password"
                        placeholder="Confirm New Password"
                        value={confirmPassword}
                        onChange={handleConfirmPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${confirmPasswordErrors.length > 0 || !passwordsMatch ? "border-red-300" : "border-gray-300"
                          }`}
                      />
                      {confirmPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {confirmPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                      {confirmPassword && !passwordsMatch && confirmPasswordErrors.length === 0 && (
                        <p className="text-red-500 text-xs mt-1">Passwords do not match</p>
                      )}
                    </div>

                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={handleChangePassword}
                        className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={handleCancelPasswordEdit}
                        className="bg-gray-500 text-white px-4 py-1.5 rounded-full hover:bg-gray-600 transition-colors cursor-pointer text-sm"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">{password}</p>
                )}
              </div>
              {!isEditingPassword && (
                <button
                  type="button"
                  onClick={handleChangePassword}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Change
                </button>
              )}
            </div>
            {/* Notification Alerts */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div>
                <p className="font-medium text-gray-700">Notification alerts</p>
                {isEditingNotifications && (
                  <div className="mt-1 flex items-center space-x-2">
                    <input
                      type="checkbox"
                      id="notifications"
                      checked={notifications}
                      onChange={toggleNotifications}
                      className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300 rounded"
                    />
                    <label htmlFor="notifications" className="text-gray-600">
                      Receive email notifications
                    </label>
                  </div>
                )}
              </div>
              <button
                type="button"
                onClick={handleChangeNotifications}
                className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
              >
                {isEditingNotifications ? "Save" : "Change"}
              </button>
            </div>
            {/* Save Button */}
            <div className="flex justify-end mt-8">
              <button
                type="submit"
                className="bg-[#EB6309] text-white px-6 py-2 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer disabled:opacity-60"
                disabled={updateLoading}
              >
                {updateLoading ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
          {/* Address Management Section */}
          <div>
            {/* Default Shipping Address */}
            <div>
              <div className="flex justify-between items-center mb-4">
                <h3 className="text-lg font-medium text-gray-800">Default shipping address</h3>
                <button
                  type="button"
                  onClick={() => handleAddAddress("ship_to")}
                  className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Add New
                </button>
              </div>
              <div className="space-y-3">
                {shippingAddresses.length === 0 ? (
                  <div className="text-gray-500 text-center py-4">No results found</div>
                ) : (
                  shippingAddresses.map((address) => (
                    <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                      <div className="flex items-start">
                        <div className="mr-3 mt-1">
                          <input
                            type="radio"
                            name="default-shipping"
                            checked={address.id === defaultShippingId}
                            onChange={() => setDefaultAddress(address.id, "ship_to")}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                            title="Select as default shipping address"
                          />
                        </div>
                        <div className="w-full">
                          <p className="font-medium">{address.clinic_name}</p>
                          <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                          <p className="text-gray-600">{address.phone_number}</p>
                        </div>
                        <div className="flex space-x-2 ml-2">
                          <button
                            type="button"
                            onClick={() => handleDeleteAddress("shipping", address.id)}
                            className="text-red-500 cursor-pointer"
                            title="Delete shipping address"
                          >
                            {/* ...delete icon svg... */}
                            <span>Delete</span>
                          </button>
                          <button
                            type="button"
                            onClick={() => handleEditAddress(address)}
                            className="text-orange-500 cursor-pointer"
                            title="Edit shipping address"
                          >
                            {/* ...edit icon svg... */}
                            <span>Edit</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>

            {/* Default Billing Address */}
            <div>
              <div className="flex justify-between items-center my-4">
                <h3 className="text-lg font-medium text-gray-800">Default billing address</h3>
                <button
                  type="button"
                  onClick={() => handleAddAddress("bill_to")}
                  className="bg-[#EB6309] text-white px-3 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors text-sm cursor-pointer"
                >
                  Add New
                </button>
              </div>
              <div className="space-y-3">
                {billingAddresses.length === 0 ? (
                  <div className="text-gray-500 text-center py-4">No results found</div>
                ) : (
                  billingAddresses.map((address) => (
                    <div key={address.id} className="border border-gray-200 rounded-md p-3 relative">
                      <div className="flex items-start">
                        <div className="mr-3 mt-1">
                          <input
                            type="radio"
                            name="default-billing"
                            checked={address.id === defaultBillingId}
                            onChange={() => setDefaultAddress(address.id, "bill_to")}
                            className="h-4 w-4 text-orange-600 focus:ring-orange-500 border-gray-300"
                            title="Select as default billing address"
                          />
                        </div>
                        <div className="w-full">
                          <p className="font-medium">{address.clinic_name}</p>
                          <p className="text-gray-600">{`${address.street_address}, (#${address.postal_code}), ${address.city}`}</p>
                          <p className="text-gray-600">{address.phone_number}</p>
                        </div>
                        <div className="flex space-x-2 ml-2">
                          <button
                            type="button"
                            onClick={() => handleDeleteAddress("billing", address.id)}
                            className="text-red-500 cursor-pointer"
                            title="Delete billing address"
                          >
                            {/* ...delete icon svg... */}
                            <span>Delete</span>
                          </button>
                          <button
                            type="button"
                            onClick={() => handleEditAddress(address)}
                            className="text-orange-500 cursor-pointer"
                            title="Edit billing address"
                          >
                            {/* ...edit icon svg... */}
                            <span>Edit</span>
                          </button>
                        </div>
                      </div>
                    </div>
                  ))
                )}
              </div>
            </div>
          </div>
        </form>
      </div>
      <AddressModal
        modalOpen={addressModalOpen}
        modalType={addressModalType}
        addressType={addressType}
        modalData={addressModalData}
        handleAddressChange={handleAddressChange}
        handleAddressSubmit={handleAddressSubmit}
        setModalOpen={setAddressModalOpen}
      />
      <DeleteConfirmationModal
        confirmModalOpen={confirmModalOpen}
        addressToDelete={addressToDelete}
        cancelDeleteAddress={cancelDeleteAddress}
        confirmDeleteAddress={confirmDeleteAddress}
      />
    </>
  )
}

export default DrProfile
