"use client";
import { use<PERSON>allback, useEffect } from "react";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { CommenProps } from "./ArchToTreat_1";
import { FieldValues, Path, PathValue, UseFormSetValue } from "react-hook-form";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";

const upperTeeth = [
  "1.8",
  "1.7",
  "1.6",
  "1.5",
  "1.4",
  "1.3",
  "1.2",
  "1.1",
  "2.1",
  "2.2",
  "2.3",
  "2.4",
  "2.5",
  "2.6",
  "2.7",
  "2.8",
];
const lowerTeeth = [
  "4.8",
  "4.7",
  "4.6",
  "4.5",
  "4.4",
  "4.3",
  "4.2",
  "4.1",
  "3.1",
  "3.2",
  "3.3",
  "3.4",
  "3.5",
  "3.6",
  "3.7",
  "3.8",
];

const upperTeethDefination = [
  "5.5",
  "5.4",
  "5.3",
  "5.2",
  "5.1",
  "6.1",
  "6.2",
  "6.3",
  "6.4",
  "6.5",
];

const lowerTeethDefination = [
  "8.5",
  "8.4",
  "8.3",
  "8.2",
  "8.1",
  "7.1",
  "7.2",
  "7.3",
  "7.4",
  "7.5",
];

export interface SpecialProps<T extends FieldValues> extends CommenProps<T> {
  setValue: UseFormSetValue<T>;
}

const Extraction = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  const extractionOption = watch("extraction.option" as Path<T>);
  const missingTeeth = watch("teethInformation.missingTeeth" as Path<T>);
  const primaryAllowedTeeth = watch(
    "teethInformation.primaryDefinationTeeth" as Path<T>,
  );

  const extractionTeeth = watch("extraction.extractionTeeth" as Path<T>);
  const extractionPrimaryTeeth = watch(
    "extraction.primaryDefinationTeeth" as Path<T>,
  );

  const updateTeeth = useCallback(() => {
    if (!Array.isArray(missingTeeth) || missingTeeth.length === 0) return;
    const updated = extractionTeeth.filter((tooth: string) => {
      return !missingTeeth.includes(tooth);
    });
    // Only set if changed
    const isSame: boolean =
      updated.length === extractionTeeth.length &&
      updated.every((v: string, i: number) => v === extractionTeeth[i]);
    if (!isSame) {
      setValue("extraction.extractionTeeth" as Path<T>, updated);
    }
  }, [missingTeeth, extractionTeeth, setValue]);

  // Memoize the updateTeethPrimary function with useCallback
  const updateTeethPrimary = useCallback(() => {
    if (!Array.isArray(primaryAllowedTeeth) || primaryAllowedTeeth.length === 0)
      return;
    if (primaryAllowedTeeth.length > 0) {
      const updated = extractionPrimaryTeeth.filter((tooth: string) => {
        return primaryAllowedTeeth.includes(tooth);
      });
      // Only set if changed
      const isSame =
        updated.length === extractionPrimaryTeeth.length &&
        updated.every(
          (v: string, i: number) => v === extractionPrimaryTeeth[i],
        );
      if (!isSame) {
        setValue("extraction.primaryDefinationTeeth" as Path<T>, updated);
      }
    }
  }, [primaryAllowedTeeth, extractionPrimaryTeeth, setValue]);

  // Memoize the extraction option effect
  const handleExtractionOption = useCallback(() => {
    if (extractionOption === "none") {
      setValue(
        "extraction.extractionTeeth" as Path<T>,
        [] as unknown as PathValue<T, Path<T>>,
      );
      setValue(
        "extraction.primaryDefinationTeeth" as Path<T>,
        [] as unknown as PathValue<T, Path<T>>,
      );
    }
  }, [extractionOption, setValue]);

  // Use effects with proper dependencies
  useEffect(() => {
    updateTeeth();
  }, [updateTeeth]);

  useEffect(() => {
    updateTeethPrimary();
  }, [updateTeethPrimary]);

  useEffect(() => {
    handleExtractionOption();
  }, [handleExtractionOption]);

  return (
    <div>
      <h3 className="font-bold text-lg  text-dark mb-2">
        {`${number}`} Extraction
      </h3>
      <div className="space-y-2 text-sm text-gray-700">
        <div className="flex gap-3">
          <label className="flex items-center gap-2">
            <RoundRadioButton
              id="extraction-none"
              label="None"
              value="none"
              register={register}
              name="extraction.option"
              labelClass="!text-[#434343] text-base"
            />
          </label>
          <label className="flex items-start gap-2">
            <RoundRadioButton
              id="extraction-specific"
              label="Extraction (Please select teeth to be extracted)"
              value="extraction"
              register={register}
              name="extraction.option"
              labelClass="!text-[#434343] text-base"
            />
          </label>
        </div>

        {extractionOption === "extraction" && (
          <div className="sm:px-10 px-5">
            <div>
              <div className="flex items-center gap-4">
                <div className="font-bold text-xl text-dark">R</div>
                <div className="flex flex-col gap-2">
                  <div className="flex gap-2 items-center">
                    {upperTeeth.map((teeth: string, index: number) => {
                      const disabled = missingTeeth.includes(teeth);
                      return (
                        <StepsCheckBoxRegister
                          register={register(
                            "extraction.extractionTeeth" as Path<T>,
                          )}
                          disabled={disabled}
                          disabledClass="!bg-dark"
                          key={index}
                          label={teeth}
                          id={`extraction-${teeth}-${index}`}
                          value={teeth}
                        />
                      );
                    })}
                  </div>

                  <div className="border-t border-t-gray"></div>

                  <div className="flex gap-2 items-center">
                    {lowerTeeth.map((teeth: string, index: number) => {
                      const disabled = missingTeeth.includes(teeth);
                      return (
                        <StepsCheckBoxRegister
                          register={register(
                            "extraction.extractionTeeth" as Path<T>,
                          )}
                          disabled={disabled}
                          disabledClass="!bg-dark"
                          key={index}
                          label={teeth}
                          id={`extraction-${teeth}-${index}`}
                          value={teeth}
                          rootLableClassName="!flex-col"
                        />
                      );
                    })}
                  </div>
                </div>
                <div className="font-bold text-xl text-dark">L</div>
              </div>
            </div>

            <p className="mb-7 mt-3">
              Note: Black represents missing tooth, Red represents movement
              restricted tooth
            </p>
            {primaryAllowedTeeth.length > 0 && (
              <div>
                <div>
                  <div className="flex items-center gap-4">
                    <div className="font-bold text-xl text-dark">R</div>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-8 items-center">
                        {upperTeethDefination.map(
                          (teeth: string, index: number) => {
                            const disabled =
                              primaryAllowedTeeth.includes(teeth);
                            return (
                              <StepsCheckBoxRegister
                                register={register(
                                  "extraction.primaryDefinationTeeth" as Path<T>,
                                )}
                                disabled={!disabled}
                                fadeOnDisable={true}
                                key={index}
                                label={teeth}
                                id={`extraction-movement-primary-${teeth}-${index}`}
                                value={teeth}
                                className={
                                  "!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"
                                }
                              />
                            );
                          },
                        )}
                      </div>

                      <div className="border-t border-t-gray"></div>

                      <div className="flex gap-8 items-center">
                        {lowerTeethDefination.map(
                          (teeth: string, index: number) => {
                            const disabled =
                              primaryAllowedTeeth.includes(teeth);
                            return (
                              <StepsCheckBoxRegister
                                register={register(
                                  "extraction.primaryDefinationTeeth" as Path<T>,
                                )}
                                disabled={!disabled}
                                fadeOnDisable={true}
                                key={index}
                                label={teeth}
                                id={`extraction-movement-primary-${teeth}-${index}`}
                                value={teeth}
                                rootLableClassName="!flex-col"
                                className={
                                  "!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"
                                }
                              />
                            );
                          },
                        )}
                      </div>
                    </div>
                    <div className="font-bold text-xl text-dark">L</div>
                  </div>
                </div>
                <p className="mb-7 mt-3">
                  Note: Click the primary dentition you do not want any
                  attachment on.
                </p>
              </div>
            )}
          </div>
        )}
        {errors.extraction &&
          "extractionTeeth" in errors.extraction &&
          errors.extraction.extractionTeeth &&
          "message" in errors.extraction.extractionTeeth &&
          errors.extraction.extractionTeeth.message && (
            <p className="text-red-500 text-sm mt-1">
              {String(errors.extraction.extractionTeeth.message)}
            </p>
          )}
      </div>
    </div>
  );
};

export default Extraction;
