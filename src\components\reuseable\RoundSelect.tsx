"use client";

import React, { useRef } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import arrowDown from "../../../public/svgs/arrow-down.svg";
import Image from "next/image";

interface RoundSelectProps {
  register: UseFormRegisterReturn;
  options: { value: string; label: string }[];
  id: string;
}

const RoundSelect: React.FC<RoundSelectProps> = ({ register, options, id }) => {
  const selectRef = useRef<HTMLSelectElement | null>(null);

  const mergedRef = (el: HTMLSelectElement) => {
    if (typeof register.ref === "function") {
      register.ref(el);
    } else if (register.ref) {
      (
        register.ref as React.MutableRefObject<HTMLSelectElement | null>
      ).current = el;
    }
    selectRef.current = el;
  };

  const handleClick = () => {
    console.log("first");
    selectRef.current?.focus();
  };

  return (
    <div
      onClick={handleClick}
      className="border-2  px-5 border-gray/20 rounded-full cursor-pointer flex flex-row items-center"
    >
      <select
        id={id}
        {...register}
        ref={mergedRef}
        className="w-full py-4  text-gray-700 outline-none cursor-pointer appearance-none bg-white pr-6"
      >
        {options.map(({ value, label }) => (
          <option
            className="cursor-pointer text-dark font-semibold"
            key={value}
            value={value}
          >
            {label}
          </option>
        ))}
      </select>
      <div className="relative w-4 h-4 pointer-events-none -ml-6">
        <Image fill src={arrowDown} alt="Arrow down" />
      </div>
    </div>
  );
};

export default RoundSelect;
