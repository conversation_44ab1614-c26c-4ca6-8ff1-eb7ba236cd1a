"use client";
import { useCallback, useEffect } from "react";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { SpecialProps } from "./MovementResctiction_2";
import { Path, PathValue, FieldValues } from "react-hook-form";

const MidLine = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  const midlineValue = watch("midline.option" as Path<T>);

  // Memoize the function that updates the form values based on midlineValue
  const handleMidlineOptionChange = useCallback(() => {
    if (midlineValue !== "improveMidlineWithIPR") {
      setValue(
        "midline.midlineIPRUpper" as Path<T>,
        false as PathValue<T, Path<T>>,
      );
      setValue(
        "midline.midlineIPRLower" as Path<T>,
        false as PathValue<T, Path<T>>,
      );
    }
  }, [midlineValue, setValue]);

  // Use the memoized function in useEffect
  useEffect(() => {
    handleMidlineOptionChange();
  }, [handleMidlineOptionChange]);

  return (
    <div>
      <div className="flex items-center justify-between mb-4">
        <h3 className="font-bold text-lg  text-dark">{`${number}`} Midline</h3>
      </div>
      <div className="mt-3 flex gap-3">
        <RoundRadioButton
          id="show-resulting-midline"
          label="Show resulting midline after alignment"
          value="showResultingMidline"
          register={register}
          name="midline.option"
          labelClass="!text-dark text-base"
        />

        <RoundRadioButton
          id="maintain-initial-midline"
          label="Maintain initial midline (may require IPR)"
          value="maintainInitialMidline"
          register={register}
          name="midline.option"
          labelClass="!text-dark text-base"
        />

        <RoundRadioButton
          id="improve-midline-with-ipr"
          label="Improve midline with IPR"
          value="improveMidlineWithIPR"
          register={register}
          name="midline.option"
          labelClass="!text-dark text-base"
        />
        {errors.midline &&
          "option" in errors.midline &&
          typeof errors.midline.option === "object" &&
          errors.midline.option !== null &&
          "message" in errors.midline.option &&
          (errors.midline.option as { message?: string }).message ===
            "Please select a midline option." && (
            <p className="text-red-500 text-sm mt-2">
              {(errors.midline.option as { message?: string }).message}
            </p>
          )}
      </div>

      {midlineValue === "improveMidlineWithIPR" && (
        <div className="ml-8 mt-3 ">
          <div className="flex gap-3">
            <StepsCheckBoxRegister
              id="upper"
              label="Upper"
              register={register("midline.midlineIPRUpper" as Path<T>)}
              labelClass="!text-dark !text-base"
              rootLableClassName="!flex-row"
              className="!w-5 !h-5"
            />

            {watch("midline.midlineIPRUpper" as Path<T>) && (
              <div className="mt-2 flex gap-3 ps-8">
                <RoundRadioButton
                  id="upper-midline-right"
                  label="to patient’s right"
                  value="toPatientsRight"
                  register={register}
                  name="midline.upperMidlineShift"
                  labelClass="!text-dark !text-base"
                />
                <RoundRadioButton
                  id="upper-midline-left"
                  label="to patient’s left"
                  value="toPatientsLeft"
                  register={register}
                  name="midline.upperMidlineShift"
                  labelClass="!text-dark !text-base"
                />
                {errors.midline &&
                  "upperMidlineShift" in errors.midline &&
                  errors.midline.upperMidlineShift &&
                  typeof errors.midline.upperMidlineShift === "object" &&
                  "message" in errors.midline.upperMidlineShift && (
                    <p className="text-red-500 text-sm mt-1">
                      {
                        (
                          errors.midline.upperMidlineShift as {
                            message?: string;
                          }
                        ).message
                      }
                    </p>
                  )}
              </div>
            )}
          </div>

          <div className="flex gap-3">
            <StepsCheckBoxRegister
              id="lower"
              label="Lower"
              register={register("midline.midlineIPRLower" as Path<T>)}
              className="!w-5 !h-5"
              labelClass="!text-dark !text-base"
              rootLableClassName="!flex-row"
            />
            {watch("midline.midlineIPRLower" as Path<T>) && (
              <div className="mt-2 flex gap-3 ps-8">
                <RoundRadioButton
                  id="lower-midline-right"
                  label="to patient’s right"
                  value="toPatientsRight"
                  register={register}
                  name="midline.lowerMidlineShift"
                  labelClass="!text-dark !text-base"
                />
                <RoundRadioButton
                  id="lower-midline-left"
                  label="to patient’s left"
                  value="toPatientsLeft"
                  register={register}
                  name="midline.lowerMidlineShift"
                  labelClass="!text-dark !text-base"
                />
                {errors.midline &&
                  "lowerMidlineShift" in errors.midline &&
                  errors.midline.lowerMidlineShift &&
                  typeof errors.midline.lowerMidlineShift === "object" &&
                  "message" in errors.midline.lowerMidlineShift && (
                    <p className="text-red-500 text-sm mt-1">
                      {
                        (
                          errors.midline.lowerMidlineShift as {
                            message?: string;
                          }
                        ).message
                      }
                    </p>
                  )}
              </div>
            )}
          </div>

          {errors.midline &&
            "option" in errors.midline &&
            errors.midline.option &&
            typeof errors.midline.option === "object" &&
            "message" in errors.midline.option && (
              <p className="text-red-500 text-sm mt-1">
                {(errors.midline.option as { message?: string }).message}
              </p>
            )}
        </div>
      )}
    </div>
  );
};

export default MidLine;
