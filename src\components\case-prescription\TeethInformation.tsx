"use client";
import { use<PERSON>allback, useEffect } from "react";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { CommenProps, DefaultTransition } from "./ArchToTreat_1";
import { FieldValues, Path, PathValue, UseFormSetValue } from "react-hook-form";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { AnimatePresence, motion } from "framer-motion";

const upperTeeth = [
  "1.8",
  "1.7",
  "1.6",
  "1.5",
  "1.4",
  "1.3",
  "1.2",
  "1.1",
  "2.1",
  "2.2",
  "2.3",
  "2.4",
  "2.5",
  "2.6",
  "2.7",
  "2.8",
];
const lowerTeeth = [
  "4.8",
  "4.7",
  "4.6",
  "4.5",
  "4.4",
  "4.3",
  "4.2",
  "4.1",
  "3.1",
  "3.2",
  "3.3",
  "3.4",
  "3.5",
  "3.6",
  "3.7",
  "3.8",
];

const upperTeethDefination = [
  "5.5",
  "5.4",
  "5.3",
  "5.2",
  "5.1",
  "6.1",
  "6.2",
  "6.3",
  "6.4",
  "6.5",
];

const lowerTeethDefination = [
  "8.5",
  "8.4",
  "8.3",
  "8.2",
  "8.1",
  "7.1",
  "7.2",
  "7.3",
  "7.4",
  "7.5",
];

export interface SpecialProps<T extends FieldValues> extends CommenProps<T> {
  setValue: UseFormSetValue<T>;
}

const TeethInformation = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  const missingTeethOption = watch(
    "teethInformation.missingTeethOption" as Path<T>,
  );
  const primaryDefinationOption = watch(
    "teethInformation.primaryDefinationOption" as Path<T>,
  );
  const handleMissingTeethOption = useCallback(() => {
    if (missingTeethOption == "none") {
      setValue(
        "teethInf ormation.missingTeeth" as Path<T>,
        [] as unknown as PathValue<T, Path<T>>,
      );
    }
  }, [missingTeethOption, setValue]);

  const handlePrimaryDefinationOption = useCallback(() => {
    if (primaryDefinationOption == "none") {
      setValue(
        "teethInformation.primaryDefinationTeeth" as Path<T>,
        [] as unknown as PathValue<T, Path<T>>,
      );
    }
  }, [primaryDefinationOption, setValue]);

  // Use effects with memoized handlers
  useEffect(() => {
    handleMissingTeethOption();
  }, [handleMissingTeethOption]);

  useEffect(() => {
    handlePrimaryDefinationOption();
  }, [handlePrimaryDefinationOption]);

  // function getNestedError<T extends FieldValues>(
  //     errors: FieldErrors<T>,
  //     path: string[]
  // ): FieldError | undefined {
  //     let current: unknown = errors;
  //     for (const key of path) {
  //         if (
  //             typeof current === "object" &&
  //             current !== null &&
  //             key in (current as Record<string, unknown>)
  //         ) {
  //             current = (current as Record<string, unknown>)[key];
  //         } else {
  //             return undefined;
  //         }
  //     }
  //     // Only return if it's actually a FieldError (has a message property)
  //     if (
  //         typeof current === "object" &&
  //         current !== null &&
  //         "message" in (current as FieldError)
  //     ) {
  //         return current as FieldError;
  //     }
  //     return undefined;
  // }

  // // Usage example in your component:
  // const optionError = getNestedError(errors, ["sagitalrRelationShip", side, "option"]);

  return (
    <div>
      <h3 className="font-bold text-lg  text-dark mb-2">
        {`${number}`} Teeth Information{" "}
        <span className="text-sm">
          {"(ex. bridges, ankylosed teeth, implants, etc.)"}
        </span>
      </h3>
      <div className="space-y-2 text-sm text-gray-700 mb-5">
        <h4 className="font-semibold text-dark">Missing Teeth</h4>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2">
            <RoundRadioButton
              id="missingTeeth-none"
              label="None"
              value="none"
              register={register}
              name="teethInformation.missingTeethOption"
              labelClass="!text-[#434343] text-base"
            />
          </label>
          <label className="flex items-start gap-2">
            <RoundRadioButton
              id="missingTeeth-missing"
              label="Missing Teeth"
              value="missingTeeth"
              register={register}
              name="teethInformation.missingTeethOption"
              labelClass="!text-[#434343] text-base"
            />
          </label>
        </div>

        <AnimatePresence initial={false} mode="wait">
          {missingTeethOption === "missingTeeth" && (
            <motion.div
              key="upper"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={DefaultTransition}
              style={{ overflow: "hidden" }}
            >
              <div className="px-10">
                <div className="flex items-center gap-4">
                  <div className="font-bold text-xl text-dark">R</div>
                  <div className="flex flex-col gap-2">
                    <div className="flex gap-2 items-center">
                      {upperTeeth.map((teeth: string, index: number) => {
                        return (
                          <StepsCheckBoxRegister
                            register={register(
                              "teethInformation.missingTeeth" as Path<T>,
                            )}
                            key={index}
                            label={teeth}
                            id={`${teeth}-${index}`}
                            value={teeth}
                          />
                        );
                      })}
                    </div>

                    <div className="border-t border-t-gray"></div>

                    <div className="flex gap-2 items-center">
                      {lowerTeeth.map((teeth: string, index: number) => {
                        return (
                          <StepsCheckBoxRegister
                            register={register(
                              "teethInformation.missingTeeth" as Path<T>,
                            )}
                            key={index}
                            label={teeth}
                            id={`${teeth}-${index}`}
                            value={teeth}
                            rootLableClassName="!flex-col"
                          />
                        );
                      })}
                    </div>
                  </div>
                  <div className="font-bold text-xl text-dark">L</div>
                </div>
              </div>

              {errors.teethInformation &&
                typeof errors.teethInformation === "object" &&
                errors.teethInformation !== null &&
                "missingTeeth" in errors.teethInformation &&
                errors.teethInformation.missingTeeth &&
                typeof errors.teethInformation.missingTeeth === "object" &&
                errors.teethInformation.missingTeeth !== null &&
                "message" in errors.teethInformation.missingTeeth &&
                (errors.teethInformation.missingTeeth as { message?: string })
                  .message && (
                  <p className="text-red-500 text-sm mt-1">
                    {String(
                      (
                        errors.teethInformation.missingTeeth as {
                          message?: string;
                        }
                      ).message,
                    )}
                  </p>
                )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>

      <div className="space-y-2 text-sm text-gray-700">
        <h4 className="font-semibold text-dark">Primary Dentition</h4>
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2">
            <RoundRadioButton
              id="primaryDefination-none"
              label="None"
              value="none"
              register={register}
              name="teethInformation.primaryDefinationOption"
              labelClass="!text-[#434343] text-base"
            />
          </label>
          <label className="flex items-start gap-2">
            <RoundRadioButton
              id="primaryDefination-specific"
              label="Primary Dentition"
              value="primaryDefinition"
              register={register}
              name="teethInformation.primaryDefinationOption"
              labelClass="!text-[#434343] text-base"
            />
          </label>
        </div>

        <AnimatePresence initial={false} mode="wait">
          {primaryDefinationOption === "primaryDefinition" && (
            <motion.div
              key="upper"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={DefaultTransition}
              style={{ overflow: "hidden" }}
            >
              <div className="px-10">
                <div className="flex items-center gap-4">
                  <div className="font-bold text-xl text-dark">R</div>
                  <div className="flex flex-col gap-2">
                    <div className="flex gap-8 items-center">
                      {upperTeethDefination.map(
                        (teeth: string, index: number) => {
                          return (
                            <StepsCheckBoxRegister
                              register={register(
                                "teethInformation.primaryDefinationTeeth" as Path<T>,
                              )}
                              key={index}
                              label={teeth}
                              id={`primary-${teeth}-${index}`}
                              value={teeth}
                            />
                          );
                        },
                      )}
                    </div>

                    <div className="border-t border-t-gray"></div>

                    <div className="flex gap-8 items-center">
                      {lowerTeethDefination.map(
                        (teeth: string, index: number) => {
                          return (
                            <StepsCheckBoxRegister
                              register={register(
                                "teethInformation.primaryDefinationTeeth" as Path<T>,
                              )}
                              key={index}
                              label={teeth}
                              id={`primary-${teeth}-${index}`}
                              value={teeth}
                              rootLableClassName="!flex-col"
                            />
                          );
                        },
                      )}
                    </div>
                  </div>
                  <div className="font-bold text-xl text-dark">L</div>
                </div>
              </div>
              {typeof errors.teethInformation === "object" &&
                errors.teethInformation !== null &&
                "primaryDefinationTeeth" in errors.teethInformation &&
                errors.teethInformation.primaryDefinationTeeth &&
                typeof errors.teethInformation.primaryDefinationTeeth ===
                  "object" &&
                errors.teethInformation.primaryDefinationTeeth !== null &&
                "message" in errors.teethInformation.primaryDefinationTeeth &&
                errors.teethInformation.primaryDefinationTeeth.message && (
                  <p className="text-red-500 text-sm mt-1">
                    {String(
                      errors.teethInformation.primaryDefinationTeeth.message,
                    )}
                  </p>
                )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default TeethInformation;
