"use client";
import React, { useEffect, useState } from "react";
import UploadFile from "./UploadFile";
import {
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import type { ScanFormData } from "./PatientRecords";
import { PatientFileData } from "@/types/types";

interface Props {
  register: UseFormRegister<ScanFormData>;
  watch: UseFormWatch<ScanFormData>;
  setValue: UseFormSetValue<ScanFormData>;
  decideLater: boolean;
  patientData?: PatientFileData | null;
}

const StlUploads: React.FC<Props> = ({
  decideLater,
  watch,
  setValue,
  register,
  patientData,
}) => {
  // Add state for STL files
  const [stlFile1, setStlFile1] = useState<File | string | null>(null);
  console.log("🚀 ~ stlFile1:", stlFile1);
  const [stlFile2, setStlFile2] = useState<File | string | null>(null);
  console.log("🚀 ~ stlFile2:", stlFile2);

  // Watch form data for changes from parent
  useEffect(() => {
    const scanData = watch("scan");
    if (scanData) {
      if (scanData.stlFile1) {
        setStlFile1(scanData.stlFile1);
      }
      if (scanData.stlFile2) {
        setStlFile2(scanData.stlFile2);
      }
    }
  }, [watch("scan")]);

  return (
    <div className="grid grid-cols-2 gap-2">
      <div className="flex flex-col gap-2">
        <span className={`cursor-pointer !text-dark font-semibold`}>Upper</span>
        <UploadFile
          register={register}
          setValue={setValue}
          watch={watch}
          name={"stlFile1" as keyof ScanFormData}
          disabled={decideLater}
          patientData={patientData}
        />
      </div>
      <div className="flex flex-col gap-2">
        <span className={`cursor-pointer !text-dark font-semibold`}>Lower</span>
        <UploadFile
          register={register}
          setValue={setValue}
          watch={watch}
          name={"stlFile2" as keyof ScanFormData}
          disabled={decideLater}
          patientData={patientData}
        />
      </div>
    </div>
  );
};

export default StlUploads;
