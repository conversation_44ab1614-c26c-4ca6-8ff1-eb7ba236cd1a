import {
  FieldErrors,
  UseFormRegister,
  UseFormSetValue,
  UseFormWatch,
} from "react-hook-form";
import { ScanFormData } from "./PatientRecords";
import React from "react";
import StlUploads from "./StlUploads";
import UploadFile from "./UploadFile";
import { PatientFileData } from "@/types/types";

interface ScanSectionProps {
  register: UseFormRegister<ScanFormData>;
  watch: UseFormWatch<ScanFormData>;
  setValue: UseFormSetValue<ScanFormData>;
  errors: FieldErrors<ScanFormData>;
  Enablecbct?: boolean;
  patientData?: PatientFileData | null;
}

const ScanSection: React.FC<ScanSectionProps> = ({
  patientData,
  register,
  watch,
  setValue,
  errors,
  Enablecbct = true,
}) => {
  return (
    <div className="mb-8">
      <h2 className="text-2xl font-bold text-dark mb-4">Scan</h2>
      <div className="flex flex-col gap-1 px-3"></div>
      <div className="gap-4 !bg-white rounded-[10px] p-4">
        <StlUploads
          setValue={setValue}
          register={register}
          watch={watch}
          decideLater={false}
          patientData={patientData}
        />
        {errors.scan?.stlFile1?.message && (
          <p className="text-danger my-1">
            {typeof errors.scan?.stlFile1?.message == "string" &&
              errors.scan?.stlFile1?.message}
          </p>
        )}

        {Enablecbct === true && (
          <>
            <div className="w-full border-t border-gray mb-7"></div>
            <div className="flex flex-col gap-4">
              <label htmlFor="">
                <span className={`cursor-pointer !text-dark font-medium`}>
                  CBCT Scan
                </span>
              </label>
              <UploadFile
                register={register}
                disabled={false}
                setValue={setValue}
                watch={watch}
                name={"cbctFile" as keyof ScanFormData}
                patientData={patientData}
              />
              {errors.scan?.cbctFile?.message && (
                <p className="text-danger my-1">
                  {String(errors.scan?.cbctFile?.message)}
                </p>
              )}
            </div>
          </>
        )}
      </div>
    </div>
  );
};

export default ScanSection;
