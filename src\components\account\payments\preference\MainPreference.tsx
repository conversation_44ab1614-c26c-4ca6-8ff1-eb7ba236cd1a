"use client";
import React from "react";
import CustomButton from "../CustomButton";
import addIcon from "../../../../../public/svgs/icons8_add_1 1.svg";
import BreadCrumbs from "@/components/layouts/BreadCrumb";

const trailNumbers: Record<string, number> = {
  method: 1,
  invoicing: 2,
};

const MainPreference = () => {
  return (
    <div className="">
      <div className="flex items-cener justify-between mb-4">
        <div>
          <BreadCrumbs trailNumbers={trailNumbers} />
        </div>
        <CustomButton
          text="Add Payment Method"
          icon={addIcon}
          onClick={() => {}}
        />
      </div>
      <div className="bg-white p-4 rounded-xl">
        <h3 className="font-bold text-dark text-[1.1rem] mb-4">
          Payment Methods for Account
        </h3>
      </div>
    </div>
  );
};

export default MainPreference;
