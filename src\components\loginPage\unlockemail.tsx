"use client";
import React from "react";
import Image from "next/image";
import logo from "../../../public/images/logo1.png";

interface UnlockEmailModalProps {
  unlockEmail: string;
  setUnlockEmail: (value: string) => void;
  onSubmit: () => void;
  onClose: () => void;
}

const UnlockEmailModal: React.FC<UnlockEmailModalProps> = ({
  unlockEmail,
  setUnlockEmail,
  onSubmit,
  onClose,
}) => {
  return (
    <div className="fixed inset-0 bg-black bg-opacity-50 flex items-center justify-center z-50">
      <div className="min-h-screen authbackground bg-cover bg-center bg-no-repeat flex items-center justify-center w-full">
        <div className="w-[30%] max-md:w-[80%] md:w-[50%] lg:w-[40%] xl:w-[30%] 2xl:w-[29%] z-30">
          <div className="xl:mb-3 lg:mb-5 2xl:mb-14">
            <Image
              src={logo}
              alt="Aligners Logo"
              className="mx-auto mb-2 w-44 xl:w-52"
              width={1000}
              height={1000}
            />
          </div>
          <div className="z-10 w-full 2xl:p-[50px] lg:p-[25px] p-[20px] rounded-[40px] bg-[#4444430F] text-center border border-[#44444321] backdrop-blur-sm">
            <h1 className="text-[38px] max-sm:text-[26px] max-md:text-[28px] md:text-[32px] font-bold text-[#444443] leading-[45px] pb-[20px] lg:pb-[8px] z-40">
              Verify your Email
            </h1>

            <div className="space-y-2 text-left pt-4">
              <input
                type="email"
                placeholder="Enter your email"
                className="border border-gray-300 rounded-full w-full px-5 py-[16px] lg:py-3 focus:outline-none"
                value={unlockEmail}
                onChange={(e) => setUnlockEmail(e.target.value)}
              />
            </div>

            <div className="pt-5 lg:pt-3 xl:pt-9 flex justify-center gap-3">
              <button
                onClick={onClose}
                className="px-6 py-3 bg-gray-300 text-black rounded-full hover:bg-gray-400 transition"
              >
                Cancel
              </button>
              <button
                onClick={onSubmit}
                className="px-6 py-3 bg-orange-500 text-white rounded-full hover:bg-orange-600 transition"
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      </div>
    </div>
  );
};

export default UnlockEmailModal;
