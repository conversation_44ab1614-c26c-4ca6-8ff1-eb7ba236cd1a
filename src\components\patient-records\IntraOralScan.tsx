"use clien";
import Image from "next/image";
import FormHeading from "../reuseable/FormHeading";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import dummyImage from "../../../public/svgs/image 2.svg";
import { UseFormRegister } from "react-hook-form";
import CustomInput from "../reuseable/CustomInput";
import PrimaryButton from "../reuseable/PrimaryButton";
// import { z } from "zod";
// import { patientDataSchema } from "../scan/Scan";

// const patientDataSchema = z.object({
//     scan: z.string().min(1, "scan is required"),
//   });

// type UserFormData = z.infer<typeof patientDataSchema>;

interface props {
  scan: string;
  setScan: (value: string) => void;
  register: UseFormRegister<{ scanOption: string; scan?: string }>;
}

const scans = [
  "<PERSON><PERSON>, <PERSON><PERSON> (ba97277f-2334-4fdc-95ca-94c3caa2c39a)",
  "<PERSON><PERSON>, <PERSON><PERSON><PERSON> (250221e1-fd85-4cdb-9b1f-0427876baee0)",
];

const IntraOralScan: React.FC<props> = ({ scan, setScan, register }) => {
  return (
    <div>
      <FormHeading text="Patient name / iTero order code / 3Shape scan Id" />

      <div className="flex gap-2 items-center mb-6">
        <CustomInput
          className="!py-3 !w-[400px]"
          placeholder="Ali, Momen"
          type="text"
        />
        <PrimaryButton
          classes="!py-3 !px-10 !min-w-1"
          text="Search"
          onClick={() => {}}
        />
      </div>
      <div className=" grid xl:grid-cols-2 border grid-cols-1 border-gray rounded-xl ">
        <div className="col-span-1 border-r border-r-gray p-4">
          <FormHeading text="Choose Scan" />
          <div className="flex flex-col gap-2">
            {scans.map((sc: string, index: number) => {
              return (
                <RoundRadioButton
                  key={index}
                  onClick={(e: React.MouseEvent<HTMLInputElement>) =>
                    setScan((e.target as HTMLInputElement).value)
                  }
                  id={`${sc}-${index}`}
                  label={sc}
                  value={sc}
                  name={"scans"}
                  register={register}
                  defaultChecked={scan === sc}
                  labelClass="!text-dark font-medium"
                />
              );
            })}
          </div>
        </div>
        <div className="col-span-1 p-4 pb-8">
          <h3 className="font-bold text-xl text-dark mb-4">
            Preview{" "}
            <span className="text-dark text-lg font-normal">{`(click to enlarge)`}</span>
          </h3>
          <div className="flex flex-col gap-2">
            <p>
              <span className="font-medium text-gray">
                Patient name:{" "}
                <span className="text-dark font-medium">
                  {"Alali, Maryamba97277f-2334-4fdc-95ca-94c3caa2c39a"}
                </span>
              </span>
            </p>
            <p>
              <span className="font-medium text-gray">
                Date:{" "}
                <span className="text-dark font-medium">
                  {"Tue Mar 04 06:48:01 PST 2025"}
                </span>
              </span>
            </p>
            <Image
              src={dummyImage}
              width={150}
              height={150}
              alt="scan image"
              className="w-full"
            />
          </div>
        </div>
      </div>

      <div className="font-medium text-dark mt-4 mb-7">
        <p>
          <span className="text-primary font-bold">Learn more</span> if
          intraoral scan is not available
        </p>
        <p>
          Please verify that the scan accurately reflects the current position
          of the teeth. Using older scans may lead to potential fit issues.
        </p>
      </div>
    </div>
  );
};

export default IntraOralScan;
