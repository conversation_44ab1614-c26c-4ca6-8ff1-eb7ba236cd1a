// const API_BASE_URL = 'https://dev.api.4dgraphy.com/api/v1';
const API_BASE_URL = process.env.NEXT_PUBLIC_API_BASE_URL ;
const API_BASE_URL_SERVER_SIDE =process.env.API_BASE_URL;
console.log(" API Routes Configuration:", {
  NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
  API_BASE_URL: process.env.API_BASE_URL,
  CLIENT_BASE_URL: API_BASE_URL,
  SERVER_BASE_URL: API_BASE_URL_SERVER_SIDE,
})


export const API_ROUTES = {
  AUTH: {
    LOGIN: `${API_BASE_URL}/auth/login-user`,
    FORGET_PASSWORD: `${API_BASE_URL}/auth/forget-password`,
    CHECK_SESSION: `${API_BASE_URL}/auth/check-session`,
    UNLOCK_ACCOUNT: `${API_BASE_URL}/auth/request-unlock`,
    CHANGE_PASSWORD:`${API_BASE_URL}/auth/change-password`,
  },
  PATIENT: {
    GET_ALL_PATIENTS: `${API_BASE_URL}/doctor/patients`,
    GET_ALL_PATIENTS_FOR_SPECIALIST: `${API_BASE_URL}/specialist/patients`,
    GET_PATIENT_BY_ID: `${API_BASE_URL}/doctor/patients`,
    GET_PATIENT_BY_ID_FOR_SPECIALIST: `${API_BASE_URL}/specialist/patient`,
    ADD_PATIENT: `${API_BASE_URL}/doctor/patients`,
    ADD_CLINICAL_DATA: `${API_BASE_URL}/doctor/patients`,
    ADD_RECORDS_DATA: `${API_BASE_URL}/doctor/patients`,
    ADD_CASE_PERSCRIPTION: `${API_BASE_URL}/doctor/patients`,
    VERSION_REVIEW: `${API_BASE_URL}/doctor/patients`,
    ADD_REFINEMENT_ALIGNER: `${API_BASE_URL}/doctor/patients`,
    ADD_RETAINER_PATIENT: `${API_BASE_URL}/doctor/retainer/patients`,
    ADD_REPLACEMENT_ALIGNER: `${API_BASE_URL}/doctor/patients/aligner-replacements`,
    ADD_4DGRAPHY_RETAINER: `${API_BASE_URL}/doctor/patients/4dgraphic_retainer`,
    ADD_CBCT_FILE: `${API_BASE_URL}/doctor/patients`,
    GET_SPECIALIST: `${API_BASE_URL}/doctor/patients`,
    GET_SHARED_REVIEW: `${API_BASE_URL}/doctor/patients/shared-review`,
  },
  ADRESSES: {
    GET_ADRESS: `${API_BASE_URL}/doctor/addresses`,
    ADD_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
    DELETE_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
    UPDATE_ADDRESS: `${API_BASE_URL}/doctor/addresses`,
  },
  PLAN: {
    GET_PLANS: `${API_BASE_URL}/doctor/plans`,
  },
  EMPLOYEE: {
    GET_EMPLOYEES: `${API_BASE_URL}/doctor/employees`,
    CREATE_EMPLOYEE: `${API_BASE_URL}/doctor/employees`,
    GET_EMPLOYEE_BY_ID: `${API_BASE_URL}/doctor/employee`,
  },
  RETAINER: {
    CREATE_RETAINER_PATIENT: `${API_BASE_URL}/doctor/retainer/patients`,
  },
  PROFILE: {
    GET_PROFILE: `${API_BASE_URL}/auth/profile`,
    UPDATE_PROFILE: `${API_BASE_URL}/auth/profile`,
    DELETE_FILE: `${API_BASE_URL}/file/delete`,
    CHANGE_PASSWORD: `${API_BASE_URL}/auth/change-password`,
  },
  SPECIALIST_EMPLOYEE: {
    GET_EMPLOYEES: `${API_BASE_URL}/specialist/employees`,
    CREATE_EMPLOYEE: `${API_BASE_URL}/specialist/employees`,
    GET_EMPLOYEE_BY_ID: `${API_BASE_URL}/specialist/employee`,
  },
};



export const API_SERVER_ROUTES = {
  ADRESSES: {
    GET_ADRESS: `${API_BASE_URL_SERVER_SIDE}/doctor/addresses`,
  },
  PATIENT: {
    GET_ALL_PATIENTS: `${API_BASE_URL_SERVER_SIDE}/doctor/patients`,
    GET_ALL_PATIENTS_FOR_SPECIALIST: `${API_BASE_URL_SERVER_SIDE}/specialist/patients`,
    GET_PATIENT_BY_ID: `${API_BASE_URL_SERVER_SIDE}/doctor/patients`,
    GET_PATIENT_BY_ID_FOR_SPECIALIST: `${API_BASE_URL_SERVER_SIDE}/specialist/patient`,
    GET_SPECIALIST: `${API_BASE_URL}/doctor/patients`,
  },
  PLAN: {
    GET_PLANS: `${API_BASE_URL_SERVER_SIDE}/doctor/plans`,
  },
   EMPLOYEE: {
    GET_EMPLOYEES: `${API_BASE_URL_SERVER_SIDE}/doctor/employees`,
    GET_EMPLOYEE_BY_ID: `${API_BASE_URL_SERVER_SIDE}/doctor/employee`,
  },
    PROFILE: {
    GET_PROFILE: `${API_BASE_URL_SERVER_SIDE}/auth/profile`,
  },
  SPECIALIST_EMPLOYEE: {
    GET_EMPLOYEES: `${API_BASE_URL_SERVER_SIDE}/specialist/employees`,
    GET_EMPLOYEE_BY_ID: `${API_BASE_URL_SERVER_SIDE}/specialist/employee`,
  },
};