import { AnimatePresence, motion } from "framer-motion";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { SpecialProps } from "./MovementResctiction_2";
import { DefaultTransition } from "./ArchToTreat_1";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { FieldValues } from "react-hook-form";
// import CustomCheckbox from "../reuseable/CustomCheckbox";
import { Path } from "react-hook-form";
const AnteriorCrossBite = <T extends FieldValues>({
  register,
  errors,
  watch,
  number,
}: SpecialProps<T>) => {
  const option = watch("anteriorCrossBite.option" as Path<T>);
  return (
    <div>
      <p className="text-lg font-semibold text-gray-800">
        {`${number}`} Anterior Crossbite
      </p>

      <div className="mt-3 flex gap-3">
        <RoundRadioButton
          id="posterior-crossbite-not-correct"
          label="Do not correct"
          value="doNotCorrect"
          register={register}
          name="anteriorCrossBite.option"
          labelClass="!text-[#434343] text-base"
        />
        <RoundRadioButton
          id="posterior-crossbite-improve"
          label="Improve"
          value="improve"
          register={register}
          name="anteriorCrossBite.option"
          labelClass="!text-[#434343] text-base"
        />
        <RoundRadioButton
          id="posterior-crossbite-correct"
          label="Correct"
          value="correct"
          register={register}
          name="anteriorCrossBite.option"
          labelClass="!text-[#434343] text-base"
        />
        {errors.anteriorCrossBite &&
          "option" in errors.anteriorCrossBite &&
          errors.anteriorCrossBite.option &&
          "message" in errors.anteriorCrossBite.option && (
            <p className="text-red-500 text-sm mt-1">
              {errors.anteriorCrossBite.option.message as string}
            </p>
          )}
      </div>
      <AnimatePresence initial={false} mode="wait">
        {(option == "correct" || option == "improve") && (
          <motion.div
            key="upper"
            initial={{ height: 0, opacity: 0 }}
            animate={{ height: "auto", opacity: 1 }}
            exit={{ height: 0, opacity: 0 }}
            transition={DefaultTransition}
            style={{ overflow: "hidden" }}
          >
            <div className="ps-4 flex gap-3 mt-3">
              <StepsCheckBoxRegister
                id="anterior-crossbite-protract-upper"
                label="Protract Upper Anterior"
                value="ProtractUpperAnterior"
                register={register("anteriorCrossBite.location" as Path<T>)}
                rootLableClassName="!flex-row"
                labelClass="!text-[#434343] text-base"
              />
              <StepsCheckBoxRegister
                id="anterior-crossbite-retract-lower"
                label="Retract Lower Anterior"
                value="RetractLowerAnterior"
                register={register("anteriorCrossBite.location" as Path<T>)}
                rootLableClassName="!flex-row"
                labelClass="!text-[#434343] text-base"
              />
              {errors.anteriorCrossBite &&
                "location" in errors.anteriorCrossBite &&
                errors.anteriorCrossBite.location &&
                "message" in errors.anteriorCrossBite.location && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.anteriorCrossBite.location.message as string}
                  </p>
                )}
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </div>
  );
};

export default AnteriorCrossBite;
