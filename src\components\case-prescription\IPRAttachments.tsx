// import RoundRadioButton from "../reuseable/RoundRadioButton";
// import { SpecialProps } from "./MovementResctiction_2";

const IPRAttachments = (
  {
    // register, errors, watch
  },
) => {
  // const iprOption = watch("iprAttachments.option")
  return (
    <></>
    // <div>
    //     <p className="text-lg font-semibold text-gray-800">13. IPR Attachments</p>
    //     <div className="flex flex-col gap-3 mt-3">
    //         <div className="flex  gap-2">
    //             <RoundRadioButton
    //                 id="ipr-attachments-intervel-3"
    //                 label="Every 3 Months"
    //                 value="3 months"
    //                 register={register}
    //                 name="iprAttachments.option"
    //                 labelClass='!text-[#434343] text-base'
    //             />
    //             <RoundRadioButton
    //                 id="ipr-attachments-intervel-6"
    //                 label="Every 6 Months"
    //                 value="6 months"
    //                 register={register}
    //                 name="iprAttachments.option"
    //                 labelClass='!text-[#434343] text-base'
    //             />
    //             <RoundRadioButton
    //                 id="ipr-attachments-intervel-nosure"
    //                 label="Not sure, please write down notes below"
    //                 value="notSure"
    //                 register={register}
    //                 name="iprAttachments.option"
    //                 labelClass='!text-[#434343] text-base'
    //             />
    //             <RoundRadioButton
    //                 id="overjet-maintainInitial-no"
    //                 label="No"
    //                 value="no"
    //                 register={register}
    //                 name="iprAttachments.option"
    //                 labelClass='!text-[#434343] text-base'
    //             />
    //             {errors.iprAttachments?.option && (
    //                 <p className="text-red-500 text-sm mt-1">{errors.iprAttachments?.option.message}</p>
    //             )}
    //         </div>

    //             <div className="ps-4 flex flex-col gap-2">
    //                 {iprOption == "notSure" && (
    //                     <div className="ps-4">
    //                         <textarea
    //                             {...register("iprAttachments.note")}
    //                             className="col-span-1 h-100px w-full p-3 border border-gray/80 resize-none rounded-xl focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
    //                             placeholder="Enter any special instructions here..."
    //                         />
    //                         {errors.iprAttachments?.note && (
    //                             <p className="text-red-500 text-sm">{errors.iprAttachments?.note.message}</p>
    //                         )}

    //                     </div>
    //                 )}
    //             </div>

    //     </div>
    // </div>
  );
};

export default IPRAttachments;
