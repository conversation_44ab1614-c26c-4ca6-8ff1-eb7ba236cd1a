"use client";

interface props {
  fill?: string;
  classess?: string;
}

const ChevronDown: React.FC<props> = ({ fill = "#000000", classess }) => {
  return (
    <svg
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill={fill}
      className={classess}
    >
      <path
        d="M17.3274 5.24415L9.9999 12.5717L2.6724 5.24415C2.34698 4.91874 1.81948 4.91874 1.49406 5.24415C1.16865 5.56957 1.16865 6.09707 1.49406 6.42249L9.41073 14.3392C9.57365 14.5021 9.78656 14.5833 9.9999 14.5833C10.2132 14.5833 10.4261 14.5021 10.5891 14.3392L18.5057 6.42249C18.8311 6.09707 18.8311 5.56957 18.5057 5.24415C18.1803 4.91874 17.6528 4.91874 17.3274 5.24415Z"
        fill="white"
      />
    </svg>
  );
};

export default ChevronDown;
