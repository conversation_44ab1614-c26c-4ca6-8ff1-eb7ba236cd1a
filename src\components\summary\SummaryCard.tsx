"use client";
import { useRouter } from "next/navigation";

interface props {
  heading: string;
  href: string;
  value: string;
  borderNone?: boolean;
}

const SummaryCard: React.FC<props> = ({
  heading,
  href,
  value,
  borderNone = false,
}) => {
  const rounter = useRouter();
  return (
    <div className="col-span-1 flex items-center gap-3">
      <div>
        <div className="flex gap-6 items-center mb-2">
          <p className="text-lg font-bold text-dark">{heading}</p>
          <button
            onClick={() => rounter.push(href)}
            className="text-orange text-sm font-medium cursor-pointer underline"
          >
            Edit
          </button>
        </div>
        <p className="text-dark text-sm">{value}</p>
      </div>
      <div
        className={`h-[40%] ${borderNone === true ? "" : "border-r-2 border-r-gray/80"}`}
      ></div>
    </div>
  );
};

export default SummaryCard;
