import { SpecialProps } from "./MovementResctiction_2";
import SagitalRelationShipSide from "./SagitalRelationShipRight";
import { FieldValues } from "react-hook-form";

const SagitalrRelationShip = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  return (
    <div>
      <h3 className="font-bold text-lg  text-dark">
        {`${number}`} Sagittal Relationship
      </h3>
      <div>
        <h4 className="text-lg font-semibold text-dark">Right</h4>
        <SagitalRelationShipSide<T>
          {...{ register, watch, errors, setValue, side: "right", number }}
        />
      </div>
      <div>
        <h4 className="text-lg font-semibold text-dark">Left</h4>
        <SagitalRelationShipSide<T>
          {...{ register, watch, errors, setValue, side: "left", number }}
        />
      </div>
    </div>
  );
};

export default SagitalrRelationShip;
