"use client";
import React, { useState, useRef } from "react";

interface AccordionProps {
  title: string;
  children: React.ReactNode;
  hasBorder?: boolean; // Optional prop to control the border
}

export const Accordion: React.FC<AccordionProps> = ({
  title,
  children,
  hasBorder = true,
}) => {
  const [isOpen, setIsOpen] = useState(false);
  const contentRef = useRef<HTMLDivElement>(null); // Ref to measure content height

  return (
    <div
      className={`${
        hasBorder ? "border-[#99999966] border-solid border-b-[1px]" : ""
      }`}
    >
      <div
        className="py-2 flex justify-between items-center cursor-pointer"
        onClick={() => setIsOpen(!isOpen)}
      >
        <h3 className="text-sm font-medium text-gray-700">{title}</h3>
        <span>
          {isOpen ? (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 29 29"
              fill="none"
              className="rotate-45"
            >
              <g clipPath="url(#clip0_74_252)">
                <path
                  d="M24.2887 13.5391C24.1293 13.3871 23.9162 13.3043 23.696 13.3089L15.6904 13.3089L15.6904 5.30333C15.6919 5.19183 15.6711 5.08116 15.6291 4.97787C15.5871 4.87458 15.5248 4.78076 15.4458 4.70199C15.3285 4.58496 15.179 4.50556 15.0163 4.47394C14.8537 4.44231 14.6853 4.45989 14.5326 4.52444C14.38 4.58898 14.2501 4.69756 14.1595 4.8363C14.0689 4.97503 14.0217 5.13764 14.0239 5.30333L14.0239 13.3089L6.01833 13.3089C5.90748 13.3067 5.79729 13.3265 5.69422 13.3674C5.59116 13.4083 5.49727 13.4693 5.41808 13.5469C5.33889 13.6245 5.27597 13.7171 5.23303 13.8193C5.19008 13.9215 5.16795 14.0313 5.16795 14.1422C5.16795 14.253 5.19007 14.3628 5.23302 14.465C5.27597 14.5672 5.33889 14.6598 5.41808 14.7374C5.49727 14.815 5.59115 14.8761 5.69422 14.9169C5.79729 14.9578 5.90748 14.9777 6.01833 14.9754L14.0239 14.9754L14.0239 22.981C14.0217 23.0919 14.0415 23.202 14.0824 23.3051C14.1233 23.4082 14.1843 23.5021 14.2619 23.5812C14.3395 23.6604 14.4321 23.7234 14.5343 23.7663C14.6365 23.8093 14.7463 23.8314 14.8572 23.8314C14.968 23.8314 15.0778 23.8093 15.18 23.7663C15.2822 23.7234 15.3748 23.6604 15.4524 23.5812C15.53 23.5021 15.5911 23.4082 15.6319 23.3051C15.6728 23.202 15.6927 23.0918 15.6904 22.981V14.9754L23.696 14.9754C23.864 14.9789 24.0291 14.9316 24.1697 14.8397C24.3103 14.7477 24.4198 14.6154 24.4839 14.4601C24.548 14.3048 24.5636 14.1337 24.5288 13.9694C24.494 13.805 24.4103 13.6551 24.2887 13.5391Z"
                  fill="#444443"
                />
              </g>
              <defs>
                <clipPath id="clip0_74_252">
                  <rect
                    width="20"
                    height="20"
                    fill="white"
                    transform="translate(14.8574) rotate(45)"
                  />
                </clipPath>
              </defs>
            </svg>
          ) : (
            <svg
              xmlns="http://www.w3.org/2000/svg"
              width="22"
              height="22"
              viewBox="0 0 29 29"
              fill="none"
            >
              <g clipPath="url(#clip0_74_252)">
                <path
                  d="M24.2887 13.5391C24.1293 13.3871 23.9162 13.3043 23.696 13.3089L15.6904 13.3089L15.6904 5.30333C15.6919 5.19183 15.6711 5.08116 15.6291 4.97787C15.5871 4.87458 15.5248 4.78076 15.4458 4.70199C15.3285 4.58496 15.179 4.50556 15.0163 4.47394C14.8537 4.44231 14.6853 4.45989 14.5326 4.52444C14.38 4.58898 14.2501 4.69756 14.1595 4.8363C14.0689 4.97503 14.0217 5.13764 14.0239 5.30333L14.0239 13.3089L6.01833 13.3089C5.90748 13.3067 5.79729 13.3265 5.69422 13.3674C5.59116 13.4083 5.49727 13.4693 5.41808 13.5469C5.33889 13.6245 5.27597 13.7171 5.23303 13.8193C5.19008 13.9215 5.16795 14.0313 5.16795 14.1422C5.16795 14.253 5.19007 14.3628 5.23302 14.465C5.27597 14.5672 5.33889 14.6598 5.41808 14.7374C5.49727 14.815 5.59115 14.8761 5.69422 14.9169C5.79729 14.9578 5.90748 14.9777 6.01833 14.9754L14.0239 14.9754L14.0239 22.981C14.0217 23.0919 14.0415 23.202 14.0824 23.3051C14.1233 23.4082 14.1843 23.5021 14.2619 23.5812C14.3395 23.6604 14.4321 23.7234 14.5343 23.7663C14.6365 23.8093 14.7463 23.8314 14.8572 23.8314C14.968 23.8314 15.0778 23.8093 15.18 23.7663C15.2822 23.7234 15.3748 23.6604 15.4524 23.5812C15.53 23.5021 15.5911 23.4082 15.6319 23.3051C15.6728 23.202 15.6927 23.0918 15.6904 22.981V14.9754L23.696 14.9754C23.864 14.9789 24.0291 14.9316 24.1697 14.8397C24.3103 14.7477 24.4198 14.6154 24.4839 14.4601C24.548 14.3048 24.5636 14.1337 24.5288 13.9694C24.494 13.805 24.4103 13.6551 24.2887 13.5391Z"
                  fill="#444443"
                />
              </g>
              <defs>
                <clipPath id="clip0_74_252">
                  <rect
                    width="20"
                    height="20"
                    fill="white"
                    transform="translate(14.8574) rotate(45)"
                  />
                </clipPath>
              </defs>
            </svg>
          )}
        </span>
      </div>
      <div
        ref={contentRef}
        className="overflow-hidden transition-all duration-300"
        style={{
          height: isOpen ? `${contentRef.current?.scrollHeight}px` : "0px",
        }}
      >
        <div className="pb-2 text-sm text-gray-600">{children}</div>
      </div>
    </div>
  );
};
