"use client";

import Image from "next/image";
import closeIcon from "../../../public/svgs/icons8_multiply 1.svg";
import { z } from "zod";
import {
  FieldError,
  FieldErrors,
  useFieldArray,
  useForm,
} from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { useEffect, useState } from "react";
import { toast } from "react-toastify";
import { submitAlignerReplacement } from "@/utils/ApisHelperFunction";
import { getDecryptedToken } from "@/app/lib/auth";

interface props {
  onClose: () => void;
  patientId: string;
}

type OptionFieldError = {
  quantity?: FieldError;
  aligner?: FieldError;
  stage?: FieldError;
  setCount?: FieldError;
  stageFrom?: FieldError;
  stageTo?: FieldError;
};

type ReplacementFormErrors = {
  note?: FieldError;
  options?: OptionFieldError[];
  addType?: FieldError;
};

const optionSchema = z.object({
  quantity: z
    .number({ required_error: "Number is required" })
    .int()
    .min(0, { message: "Quantity cannot be negative" }),
  aligner: z.string().optional(),
  stage: z.string().optional(),
  stageFrom: z.string().optional(),
  stageTo: z.string().optional(),
});
export const replacementSchema = z
  .object({
    note: z.string({ required_error: "Please write a note" }).optional(),
    addType: z.enum(["bulk", "single"]),
    options: z
      .array(optionSchema, {
        required_error: "At least one option is required",
      })
      .min(1, "At least one option is required"),
  })
  .superRefine((data, ctx) => {
    data.options.forEach((option, index) => {
      // Validation for single add type
      if (data.addType === "single") {
        if (!option.aligner) {
          ctx.addIssue({
            path: ["options", index, "aligner"],
            code: z.ZodIssueCode.custom,
            message: "Please select an aligner",
          });
        }
        if (!option.stage) {
          ctx.addIssue({
            path: ["options", index, "stage"],
            code: z.ZodIssueCode.custom,
            message: "Please enter a stage number",
          });
        }
      } else {
        // Validation for bulk add type
        if (!option.aligner) {
          ctx.addIssue({
            path: ["options", index, "aligner"],
            code: z.ZodIssueCode.custom,
            message: "Please select an aligner",
          });
        }
        if (!option.stageFrom) {
          ctx.addIssue({
            path: ["options", index, "stageFrom"],
            code: z.ZodIssueCode.custom,
            message: "Stage range is required",
          });
        }
        if (!option.stageTo) {
          ctx.addIssue({
            path: ["options", index, "stageTo"],
            code: z.ZodIssueCode.custom,
            message: "Stage range is required",
          });
        }
      }
    });
  });

type replacementSchema = z.infer<typeof replacementSchema>;

const alignerOptions = [
  { value: "maxilla", label: "Maxilla" },
  { value: "mandible", label: "Mandible" },
  { value: "both", label: "Both Arches" },
  { value: "attachment", label: "Attachment template" },
];

const ReplacementModel: React.FC<props> = ({ onClose, patientId }) => {
  const [addType, setAddType] = useState<"bulk" | "single">("bulk");
  const [activeTab, setActiveTab] = useState<"form" | "summary">("form");

  const {
    register,
    watch,
    handleSubmit,
    control,
    reset,
    trigger,
    formState: { errors },
  } = useForm<replacementSchema>({
    resolver: zodResolver(replacementSchema),
    defaultValues: {
      note: "",
      addType: "bulk",
      options: [
        {
          quantity: 0,
          aligner: "",
          stage: "",
          stageFrom: "",
          stageTo: "",
        },
      ],
    },
    mode: "onChange",
  });

  const { fields, append, remove } = useFieldArray({
    control,
    name: "options",
  });

  const data = watch();
  const currentAddType = watch("addType");

  useEffect(() => {
    setAddType(currentAddType);
    // Reset fields when switching between bulk and single
    if (currentAddType === "bulk") {
      reset({
        ...data,
        options: [
          {
            quantity: 0,
            aligner: "",
            stage: "",
            stageFrom: "",
            stageTo: "",
          },
        ],
      });
    } else {
      reset({
        ...data,
        options: [
          {
            quantity: 0,
            aligner: "",
            stage: "",
            stageFrom: "",
            stageTo: "",
          },
        ],
      });
    }
  }, [currentAddType, reset]);

  const onSubmit = async (data: replacementSchema) => {
    try {
      const token = getDecryptedToken("AccessToken");
      // Stringify the whole form data for replacement_data
      const replacementData = JSON.stringify(data);
      const response = await submitAlignerReplacement(
        token as string,
        replacementData,
        patientId,
      );
      if (response && response.success) {
        toast.success(
          response?.message ||
          "Aligner replacement request submitted successfully!",
        );
        onClose();
      } else {
        toast.error(
          response?.message || "Failed to submit aligner replacement request.",
        );
      }
    } catch {
      return null;
    }
  };

  const onError = (errors: FieldErrors<replacementSchema>) => {
    toast.error("Please fill all the required fields");
    console.log("❌ Form validation errors:", errors);
  };

  const handleNextTab = async () => {
    const isFormValid = await trigger();
    if (isFormValid) {
      setActiveTab("summary");
    } else {
      toast.error("Please fill all required fields correctly");
    }
  };

  const handleEditSection = () => {
    setActiveTab("form");
  };

  const typedErrors = errors as ReplacementFormErrors;

  // Helper function to get aligner label from value
  const getAlignerLabel = (value: string) => {
    return (
      alignerOptions.find((option) => option.value === value)?.label || value
    );
  };

  return (
    <div className="bg-dark/30 w-screen h-screen fixed top-0 left-0 z-50 flex items-center justify-center">
      <div className="bg-white p-5 rounded-2xl min-w-6xl h-[520px] overflow-auto flex flex-col">
        <div className="flex items-center justify-between">
          <div className="font-bold text-xl font-manrope text-dark">
            Aligner Replacement
          </div>
          <div
            onClick={() => onClose()}
            className="w-6 h-6 relative cursor-pointer"
          >
            <Image fill src={closeIcon} alt="Close icon" />
          </div>
        </div>

        {/* Tab Navigation */}
        <div className="flex border-b border-gray-200 mb-4 mt-2">
          <button
            className={`py-2 px-4 font-semibold ${activeTab === "form" ? "text-primary border-b-2 border-primary" : "text-gray-500"}`}
            onClick={() => setActiveTab("form")}
          >
            Select Aligners
          </button>
          <button
            className={`py-2 px-4 font-semibold ${activeTab === "summary" ? "text-primary border-b-2 border-primary" : "text-gray-500"}`}
            onClick={handleNextTab}
          >
            Summary
          </button>
        </div>

        <form
          className="flex flex-col justify-between flex-grow"
          onSubmit={handleSubmit(onSubmit, onError)}
        >
          {activeTab === "form" ? (
            <>
              <div className="flex-grow">
                <h3 className="font-semibold mb-3">Add aligners</h3>

                {/* Add Type Selection */}
                <div className="flex items-center mb-4 gap-6">
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="add-bulk"
                      value="bulk"
                      className="mr-2 h-4 w-4 accent-[#EB6309]"
                      {...register("addType")}
                    />
                    <label htmlFor="add-bulk">Add in bulk</label>
                  </div>
                  <div className="flex items-center">
                    <input
                      type="radio"
                      id="add-single"
                      value="single"
                      className="mr-2 h-4 w-4 accent-[#EB6309]"
                      {...register("addType")}
                    />
                    <label htmlFor="add-single">Add single</label>
                  </div>
                </div>

                {/* Different form fields based on add type */}
                {addType === "bulk" ? (
                  // BULK ADD INTERFACE
                  <div className="flex flex-col gap-2 overflow-y-auto max-h-72 scrollbar-hidden">
                    {fields.map((field, index) => (
                      <div
                        key={field.id}
                        className="border border-gray-200 p-3 rounded-lg mb-2"
                      >
                        <div className="flex flex-row items-center justify-between">
                          <div className="flex flex-col w-full gap-4">
                            <div className="flex flex-wrap gap-4">
                              <div className="flex flex-col gap-1 min-w-[200px]">
                                <label className="text-sm text-gray-600">
                                  Select aligner
                                </label>
                                <select
                                  {...register(`options.${index}.aligner`)}
                                  className="border border-neutral-400 rounded-lg px-2 py-3"
                                >
                                  <option value="">Please select</option>
                                  {alignerOptions.map((option) => (
                                    <option
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div className="flex items-end gap-4">
                                <div className="flex flex-col gap-1">
                                  <label className="text-sm text-gray-600">
                                    Stage
                                  </label>
                                  <div className="flex items-center gap-2">
                                    <input
                                      {...register(
                                        `options.${index}.stageFrom`,
                                      )}
                                      className="border border-neutral-400 rounded-lg px-2 py-3 w-20"
                                      type="number"
                                      placeholder="From"
                                    />
                                    <span>~</span>
                                    <input
                                      {...register(`options.${index}.stageTo`)}
                                      className="border border-neutral-400 rounded-lg px-2 py-3 w-20"
                                      type="number"
                                      placeholder="To"
                                    />
                                  </div>
                                </div>
                              </div>
                            </div>
                          </div>

                          {index > 0 && (
                            <button
                              type="button"
                              className="relative w-6 h-6 cursor-pointer ml-3 bg-red-500 rounded-full flex items-center justify-center"
                              onClick={() => remove(index)}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="white"
                                width="16"
                                height="16"
                              >
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                              </svg>
                            </button>
                          )}
                        </div>

                        {/* Error messages for bulk */}
                        <div className="flex flex-wrap gap-4 mt-2">
                          {typedErrors?.options &&
                            typedErrors?.options[index]?.aligner && (
                              <p className="text-sm text-red-500">
                                {typedErrors.options[index].aligner?.message}
                              </p>
                            )}
                          {typedErrors?.options &&
                            typedErrors?.options[index]?.stageFrom && (
                              <p className="text-sm text-red-500">
                                {typedErrors.options[index].stageFrom?.message}
                              </p>
                            )}
                          {typedErrors?.options &&
                            typedErrors?.options[index]?.stageTo && (
                              <p className="text-sm text-red-500">
                                {typedErrors.options[index].stageTo?.message}
                              </p>
                            )}
                        </div>
                      </div>
                    ))}
                  </div>
                ) : (
                  // SINGLE ADD INTERFACE
                  <div className="flex flex-col gap-2 overflow-y-auto max-h-72 scrollbar-hidden">
                    {fields.map((field, index) => (
                      <div
                        key={field.id}
                        className="border border-gray-200 p-3 rounded-lg mb-2"
                      >
                        <div className="flex flex-row items-center justify-between">
                          <div className="flex flex-col w-full gap-4">
                            <div className="flex flex-wrap gap-4">
                              <div className="flex flex-col gap-1 min-w-[200px]">
                                <label className="text-sm text-gray-600">
                                  Select aligner
                                </label>
                                <select
                                  {...register(`options.${index}.aligner`)}
                                  className="border border-neutral-400 rounded-lg px-2 py-3"
                                >
                                  <option value="">Please select</option>
                                  {alignerOptions.map((option) => (
                                    <option
                                      key={option.value}
                                      value={option.value}
                                    >
                                      {option.label}
                                    </option>
                                  ))}
                                </select>
                              </div>

                              <div className="flex flex-col gap-1">
                                <label className="text-sm text-gray-600">
                                  Stage
                                </label>
                                <input
                                  {...register(`options.${index}.stage`)}
                                  className="border border-neutral-400 rounded-lg px-2 py-3 w-24"
                                  type="number"
                                  placeholder="Number"
                                />
                              </div>
                            </div>
                          </div>

                          {index > 0 && (
                            <button
                              type="button"
                              className="relative w-6 h-6 cursor-pointer ml-3 bg-red-500 rounded-full flex items-center justify-center"
                              onClick={() => remove(index)}
                            >
                              <svg
                                xmlns="http://www.w3.org/2000/svg"
                                viewBox="0 0 24 24"
                                fill="white"
                                width="16"
                                height="16"
                              >
                                <path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z" />
                              </svg>
                            </button>
                          )}
                        </div>

                        {/* Error messages */}
                        <div className="flex flex-wrap gap-4 mt-2">
                          {typedErrors?.options &&
                            typedErrors?.options[index]?.aligner && (
                              <p className="text-sm text-red-500">
                                {typedErrors.options[index].aligner?.message}
                              </p>
                            )}
                          {typedErrors?.options &&
                            typedErrors?.options[index]?.stage && (
                              <p className="text-sm text-red-500">
                                {typedErrors.options[index].stage?.message}
                              </p>
                            )}
                        </div>
                      </div>
                    ))}

                    {/* Add single stage button */}
                    {fields.length < 3 && (
                      <div className="my-4">
                        <button
                          className="bg-primary text-white px-4 py-2 rounded-md cursor-pointer"
                          type="button"
                          onClick={() =>
                            append({
                              quantity: 0,
                              aligner: "",
                              stage: "",
                              stageFrom: "",
                              stageTo: "",
                            })
                          }
                        >
                          Add single stage
                        </button>
                      </div>
                    )}
                  </div>
                )}

                <div className="flex flex-col gap-1 mt-6">
                  <label
                    className="text-dark font-semibold"
                    htmlFor="note-replacement"
                  >
                    Additional notes:
                  </label>
                  <textarea
                    {...register("note")}
                    className="w-full px-2 py-2 rounded-md border border-neutral-300"
                    placeholder="Type here..."
                    id="note-replacement"
                    rows={4}
                  ></textarea>
                </div>
              </div>

              <div className="flex items-center justify-end gap-3 mt-4">
                <button
                  onClick={onClose}
                  className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray-300 px-6"
                  type="button"
                >
                  <span className="font-semibold text-lg text-dark">
                    Cancel
                  </span>
                </button>

                <button
                  className="flex items-center justify-center py-2.5 cursor-pointer rounded-full bg-primary px-6 hover:bg-[#D45A08] transition"
                  type="button"
                  onClick={handleNextTab}
                >
                  <span className="font-semibold text-base text-white">
                    Next
                  </span>
                </button>
              </div>
            </>
          ) : (
            // Summary Tab Content
            <>
              {/* Submit button at top of summary page */}
              <div className="flex items-center justify-between mb-4">
                <h3 className="font-semibold text-lg">Summary</h3>
              </div>

              <div className="flex-grow bg-gray-50 p-4 rounded-lg">
                {/* Request Type Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
                  <div className="flex justify-between items-center">
                    <div>
                      <h4 className="font-semibold text-gray-700">
                        Request Type
                      </h4>
                      <p className="mt-1 text-md">
                        {data.addType === "bulk"
                          ? "Bulk Replacement"
                          : "Single Stage Replacement"}
                      </p>
                    </div>
                    <button
                      type="button"
                      onClick={handleEditSection}
                      className="text-primary hover:underline flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 20h9"></path>
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                      </svg>
                      <span className="ml-1">Edit</span>
                    </button>
                  </div>
                </div>

                {/* Requested Aligners Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200 mb-4">
                  <div className="flex justify-between items-center mb-3">
                    <h4 className="font-semibold text-gray-700">
                      Requested Aligners
                    </h4>
                    <button
                      type="button"
                      onClick={handleEditSection}
                      className="text-primary hover:underline flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 20h9"></path>
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                      </svg>
                      <span className="ml-1">Edit</span>
                    </button>
                  </div>

                  <div className="space-y-3">
                    {data.options.map((option, idx) => (
                      <div key={idx} className="bg-gray-50 p-3 rounded-md">
                        <div className="flex justify-between">
                          <div>
                            <p className="font-medium">
                              {getAlignerLabel(option.aligner || "")}
                            </p>
                            {data.addType === "bulk" ? (
                              <p className="text-sm text-gray-600">
                                Stages: {option.stageFrom} ~ {option.stageTo}
                              </p>
                            ) : (
                              <p className="text-sm text-gray-600">
                                Stage: {option.stage}
                              </p>
                            )}
                          </div>
                        </div>
                      </div>
                    ))}
                  </div>
                </div>

                {/* Additional Notes Section */}
                <div className="bg-white p-4 rounded-md border border-gray-200">
                  <div className="flex justify-between items-center mb-2">
                    <h4 className="font-semibold text-gray-700">
                      Additional Notes
                    </h4>
                    <button
                      type="button"
                      onClick={handleEditSection}
                      className="text-primary hover:underline flex items-center"
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="16"
                        height="16"
                        viewBox="0 0 24 24"
                        fill="none"
                        stroke="currentColor"
                        strokeWidth="2"
                        strokeLinecap="round"
                        strokeLinejoin="round"
                      >
                        <path d="M12 20h9"></path>
                        <path d="M16.5 3.5a2.121 2.121 0 0 1 3 3L7 19l-4 1 1-4L16.5 3.5z"></path>
                      </svg>
                      <span className="ml-1">Edit</span>
                    </button>
                  </div>

                  <p className="p-2 bg-gray-50 rounded-md">
                    {data.note || "No additional notes provided."}
                  </p>
                </div>
              </div>
              <div className="flex justify-end gap-3">
                <button
                  type="button"
                  onClick={handleEditSection}
                  className="flex items-center justify-center py-2 cursor-pointer rounded-full border border-gray-300 px-6"
                >
                  <span className="font-semibold text-base text-dark">
                    Back
                  </span>
                </button>
                <button
                  type="submit"
                  className="flex items-center justify-center py-2.5 cursor-pointer rounded-full bg-primary px-6 hover:bg-[#D45A08] transition"
                >
                  <span className="font-semibold text-base text-white">
                    Submit Request
                  </span>
                </button>
              </div>
            </>
          )}
        </form>
      </div>
    </div>
  );
};

export default ReplacementModel;
