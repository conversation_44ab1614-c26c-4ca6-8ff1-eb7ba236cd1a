import { useCallback, useEffect } from "react";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import { SpecialProps } from "./MovementResctiction_2";
import StepsCheckBoxRegister from "../reuseable/SquareCheckBoxRegister";
import { AnimatePresence, motion } from "framer-motion";
import { DefaultTransition } from "./ArchToTreat_1";
import { Path, PathValue, FieldValues } from "react-hook-form";

const upperTeeth = [
  "1.8",
  "1.7",
  "1.6",
  "1.5",
  "1.4",
  "1.3",
  "1.2",
  "1.1",
  "2.1",
  "2.2",
  "2.3",
  "2.4",
  "2.5",
  "2.6",
  "2.7",
  "2.8",
];
const lowerTeeth = [
  "4.8",
  "4.7",
  "4.6",
  "4.5",
  "4.4",
  "4.3",
  "4.2",
  "4.1",
  "3.1",
  "3.2",
  "3.3",
  "3.4",
  "3.5",
  "3.6",
  "3.7",
  "3.8",
];

const upperTeethDefination = [
  "5.5",
  "5.4",
  "5.3",
  "5.2",
  "5.1",
  "6.1",
  "6.2",
  "6.3",
  "6.4",
  "6.5",
];

const lowerTeethDefination = [
  "8.5",
  "8.4",
  "8.3",
  "8.2",
  "8.1",
  "7.1",
  "7.2",
  "7.3",
  "7.4",
  "7.5",
];

const AttachmentResctriction_3 = <T extends FieldValues>({
  register,
  errors,
  watch,
  setValue,
  number,
}: SpecialProps<T>) => {
  const attachmentRestriction = watch(
    "attachmentRestrictionSchema.option" as Path<T>,
  );

  const missingTeeth = watch(
    "teethInformation.missingTeeth" as Path<T>,
  ) as string[];
  const primaryAllowedTeeth = watch(
    "teethInformation.primaryDefinationTeeth" as Path<T>,
  ) as string[];

  const attachmentRestrictionTeeth = watch(
    "attachmentRestrictionSchema.restrictedTeeth" as Path<T>,
  ) as string[];
  const attachmentRestrictionPrimaryTeeth = watch(
    "attachmentRestrictionSchema.primaryDefinationTeeth" as Path<T>,
  ) as string[];

  const updateTeeth = useCallback(() => {
    if (!Array.isArray(missingTeeth) || missingTeeth.length === 0) return;
    const updated = attachmentRestrictionTeeth.filter((tooth: string) => {
      return !missingTeeth.includes(tooth);
    });
    // Only set if changed
    const isSame =
      updated.length === attachmentRestrictionTeeth.length &&
      updated.every((v, i) => v === attachmentRestrictionTeeth[i]);
    if (!isSame) {
      setValue(
        "attachmentRestrictionSchema.restrictedTeeth" as Path<T>,
        updated as PathValue<T, Path<T>>,
      );
    }
  }, [missingTeeth, attachmentRestrictionTeeth, setValue]);

  const updateTeethPrimary = useCallback(() => {
    if (!Array.isArray(primaryAllowedTeeth) || primaryAllowedTeeth.length === 0)
      return;

    if (primaryAllowedTeeth.length > 0) {
      const updated = attachmentRestrictionPrimaryTeeth.filter(
        (tooth: string) => {
          return primaryAllowedTeeth.includes(tooth);
        },
      );

      // Only set if changed
      const isSame =
        updated.length === attachmentRestrictionPrimaryTeeth.length &&
        updated.every((v, i) => v === attachmentRestrictionPrimaryTeeth[i]);
      if (!isSame) {
        setValue(
          "attachmentRestrictionSchema.primaryDefinationTeeth" as Path<T>,
          updated as PathValue<T, Path<T>>,
        );
      }
    }
  }, [primaryAllowedTeeth, attachmentRestrictionPrimaryTeeth, setValue]);

  // const updateTeethPrimary = useCallback(() => {
  //     if (!Array.isArray(primaryAllowedTeeth) || primaryAllowedTeeth.length === 0) return;

  //     if (primaryAllowedTeeth.length > 0) {
  //         const updated = attachmentRestrictionPrimaryTeeth.filter((tooth: string) => {
  //             return primaryAllowedTeeth.includes(tooth);
  //         });
  //         console.log("🚀 ~ 4 ~ updated:", updated)

  //         setValue("attachmentRestrictionSchema.primaryDefinationTeeth", updated);
  //     }
  // }, [primaryAllowedTeeth, attachmentRestrictionPrimaryTeeth, setValue]);

  // Use separate effect for setting "none" option
  const handleRestrictionChange = useCallback(() => {
    if (attachmentRestriction === "none") {
      setValue(
        "attachmentRestrictionSchema.restrictedTeeth" as Path<T>,
        [] as PathValue<T, Path<T>>,
      );
      setValue(
        "attachmentRestrictionSchema.primaryDefinationTeeth" as Path<T>,
        [] as PathValue<T, Path<T>>,
      );
    }
  }, [attachmentRestriction, setValue]);

  // Now our effects depend on memoized functions which won't change on every render
  useEffect(() => {
    updateTeeth();
  }, [updateTeeth]);

  useEffect(() => {
    updateTeethPrimary();
  }, [primaryAllowedTeeth, attachmentRestrictionPrimaryTeeth, setValue]);

  useEffect(() => {
    handleRestrictionChange();
  }, [handleRestrictionChange]);

  return (
    <div>
      <h3 className="font-bold text-lg mb-2 text-dark">
        {`${number}`} Attachments
      </h3>
      {/* <span className='text-sm'>{"(To specify attachments, see Clinical Preferences)"}</span> */}
      <div className="space-y-2 text-sm text-gray-700">
        <div className="flex items-center gap-2">
          <label className="flex items-center gap-2">
            <RoundRadioButton
              id="attachment-none"
              label="None (move all teeth)"
              value="none"
              register={register}
              name="attachmentRestrictionSchema.option"
              labelClass="!text-[#434343] text-base"
            />
          </label>
          <label className="flex items-start gap-2">
            <RoundRadioButton
              id="attachment-specific"
              label="This specific teeth should not have attachment"
              value="specific"
              register={register}
              name="attachmentRestrictionSchema.option"
              labelClass="!text-[#434343] text-base"
            />
          </label>
        </div>

        <AnimatePresence initial={false} mode="wait">
          {attachmentRestriction === "specific" && (
            <motion.div
              key="upper"
              initial={{ height: 0, opacity: 0 }}
              animate={{ height: "auto", opacity: 1 }}
              exit={{ height: 0, opacity: 0 }}
              transition={DefaultTransition}
              style={{ overflow: "hidden" }}
            >
              <div>
                <div>
                  <div className="flex items-center gap-4">
                    <div className="font-bold text-xl text-dark">R</div>
                    <div className="flex flex-col gap-2">
                      <div className="flex gap-2 items-center">
                        {upperTeeth.map((teeth: string, index: number) => {
                          const disabled = missingTeeth.includes(teeth);
                          return (
                            <StepsCheckBoxRegister
                              register={register(
                                "attachmentRestrictionSchema.restrictedTeeth" as Path<T>,
                              )}
                              disabled={disabled}
                              disabledClass="!bg-dark"
                              key={index}
                              label={teeth}
                              id={`attachment-${teeth}-${index}`}
                              value={teeth}
                            />
                          );
                        })}
                      </div>

                      <div className="border-t border-t-gray"></div>

                      <div className="flex gap-2 items-center">
                        {lowerTeeth.map((teeth: string, index: number) => {
                          const disabled = missingTeeth.includes(teeth);
                          return (
                            <StepsCheckBoxRegister
                              register={register(
                                "attachmentRestrictionSchema.restrictedTeeth" as Path<T>,
                              )}
                              disabled={disabled}
                              disabledClass="!bg-dark"
                              key={index}
                              label={teeth}
                              id={`attachment-${teeth}-${index}`}
                              value={teeth}
                              rootLableClassName="!flex-col"
                            />
                          );
                        })}
                      </div>
                    </div>
                    <div className="font-bold text-xl text-dark">L</div>
                  </div>
                </div>

                <p className="mb-7 mt-3">
                  Note: Black represents missing tooth, Red represents movement
                  restricted tooth
                </p>
                {primaryAllowedTeeth.length > 0 && (
                  <div>
                    <div>
                      <div className="flex items-center gap-4">
                        <div className="font-bold text-xl text-dark">R</div>
                        <div className="flex flex-col gap-2">
                          <div className="flex gap-8 items-center">
                            {upperTeethDefination.map(
                              (teeth: string, index: number) => {
                                const disabled =
                                  primaryAllowedTeeth.includes(teeth);
                                return (
                                  <StepsCheckBoxRegister
                                    register={register(
                                      "attachmentRestrictionSchema.primaryDefinationTeeth" as Path<T>,
                                    )}
                                    disabled={!disabled}
                                    fadeOnDisable={true}
                                    key={index}
                                    label={teeth}
                                    id={`attachment-primary-${teeth}-${index}`}
                                    value={teeth}
                                    className={
                                      "!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"
                                    }
                                  />
                                );
                              },
                            )}
                          </div>

                          <div className="border-t border-t-gray"></div>

                          <div className="flex gap-8 items-center">
                            {lowerTeethDefination.map(
                              (teeth: string, index: number) => {
                                const disabled =
                                  primaryAllowedTeeth.includes(teeth);
                                return (
                                  <StepsCheckBoxRegister
                                    register={register(
                                      "attachmentRestrictionSchema.primaryDefinationTeeth" as Path<T>,
                                    )}
                                    disabled={!disabled}
                                    fadeOnDisable={true}
                                    key={index}
                                    label={teeth}
                                    id={`attachment-primary-${teeth}-${index}`}
                                    value={teeth}
                                    rootLableClassName="!flex-col"
                                    className={
                                      "!bg-gray-600 peer-checked:!bg-primary peer-checked:!border-transparent"
                                    }
                                  />
                                );
                              },
                            )}
                          </div>
                        </div>
                        <div className="font-bold text-xl text-dark">L</div>
                      </div>
                    </div>
                    <p className="mb-7 mt-3">
                      Note: Click the primary dentition you do not want any
                      attachment on.
                    </p>
                  </div>
                )}
              </div>

              {errors.attachmentRestrictionSchema &&
                "restrictedTeeth" in errors.attachmentRestrictionSchema && (
                  <p className="text-red-500 text-sm mt-1">
                    {errors.attachmentRestrictionSchema.restrictedTeeth &&
                    typeof errors.attachmentRestrictionSchema
                      .restrictedTeeth === "object" &&
                    "message" in
                      errors.attachmentRestrictionSchema.restrictedTeeth
                      ? String(
                          errors.attachmentRestrictionSchema.restrictedTeeth
                            .message,
                        )
                      : "Invalid selection"}
                  </p>
                )}
            </motion.div>
          )}
        </AnimatePresence>
      </div>
    </div>
  );
};

export default AttachmentResctriction_3;
