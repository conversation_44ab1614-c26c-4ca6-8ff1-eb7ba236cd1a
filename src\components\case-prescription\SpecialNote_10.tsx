import { FieldValues, Path } from "react-hook-form";
import { SpecialProps } from "./MovementResctiction_2";

const SpecialNote_10 = <T extends FieldValues>({
  register,
  errors,
  number,
}: SpecialProps<T>) => {
  return (
    <div>
      <div className="flex flex-col flex-grow">
        <div className="flex items-center justify-between mb-4">
          <h3 className="font-bold text-lg  text-dark">
            {`${number}`} Special Instructions
          </h3>
        </div>
        <div className="flex flex-col flex-grow">
          <div className="grid xl:grid-cols-2 grid-cols-1 min-h-72">
            <textarea
              {...register("specialNote.note" as Path<T>)}
              className="col-span-1 h-full p-3 border border-gray/80 resize-none rounded-xl focus:ring-orange-500 focus:border-orange-500 sm:text-sm"
              placeholder="Enter any special instructions here..."
            />
          </div>
          {errors.specialNote && "note" in errors.specialNote && (
            <p className="text-red-500 text-sm mt-1">
              {
                (
                  errors.specialNote as {
                    note?: { message?: string };
                  }
                )?.note?.message
              }
            </p>
          )}
        </div>
      </div>
    </div>
  );
};

export default SpecialNote_10;
