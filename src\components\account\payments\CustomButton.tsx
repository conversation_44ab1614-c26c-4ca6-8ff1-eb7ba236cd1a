"use client";

import { StaticImport } from "next/dist/shared/lib/get-img-props";
import Image from "next/image";

interface props {
  onClick: () => void;
  text: string;
  icon?: StaticImport | string;
  mainButtonClass?: string;
}

const CustomButton: React.FC<props> = ({
  onClick,
  text,
  icon,
  mainButtonClass,
}) => {
  return (
    <button
      onClick={onClick}
      className={`text-white bg-primary rounded-full px-4 py-3 flex items-center gap-2 cursor-pointer ${mainButtonClass}`}
    >
      {icon && (
        <div className="relative w-5 h-5">
          <Image src={icon} alt="Button icon" fill className="object-cover" />
        </div>
      )}
      <span>{text}</span>
    </button>
  );
};

export default CustomButton;
