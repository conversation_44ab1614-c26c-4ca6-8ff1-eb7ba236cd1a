import { fetchApi } from "@/api/getapis"
import Dr<PERSON><PERSON><PERSON><PERSON> from "@/components/account/DrProfile.tsx/DrProfile"
import type { Address, ProfileData } from "@/types/types"
import { API_SERVER_ROUTES } from "@/utils/ApiRoutes"
export const dynamic = "force-dynamic";
const page = async () => {
  let data: Address[] = []
  let Profiledata: ProfileData | null = null

  try {
    const addressResponse = await fetchApi(`${API_SERVER_ROUTES.ADRESSES.GET_ADRESS}`)
    // Ensure we have a valid array response
    if (Array.isArray(addressResponse)) {
      data = addressResponse as Address[]
    } else if (addressResponse && typeof addressResponse === "object" && "data" in addressResponse) {
      // Handle case where response is wrapped in a data object
      data = Array.isArray(addressResponse.data) ? addressResponse.data : []
    }
  } catch (error) {
    console.error("Error fetching addresses:", error)
    data = []
  }

  try {
    const profileResponse = await fetchApi(`${API_SERVER_ROUTES.PROFILE.GET_PROFILE}`)
    if (profileResponse && typeof profileResponse === "object") {
      Profiledata = profileResponse as ProfileData
    }
  } catch (error) {
    console.error("Error fetching profile data:", error)
    Profiledata = null
  }

  return (
    <>
      <DrProfile
        data={data}
        Profiledata={
          Profiledata || {
            data: null,
            id: 0,
            username: "",
            first_name: "",
            last_name: "",
            email: "",
            profile_image: null,
            is_active: false,
            is_verified: false,
            is_deleted: false,
            created_at: "",
            updated_at: "",
            failed_login_attempts: 0,
            last_failed_login_at: null,
            is_locked: false,
            role_id: 0,
            specialist_id: null,
            user_uuid: "",
          }
        }
      />
    </>
  )
}

export default page
