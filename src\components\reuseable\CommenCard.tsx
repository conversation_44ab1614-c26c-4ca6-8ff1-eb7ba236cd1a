"use client";

import PrimaryButton from "./PrimaryButton";

interface props {
  children?: React.ReactNode;
  classes?: string;
  header: string;
  buttonText: string;
  value: string;
  selectedValue: string;
  selectedText?: string;
  onClickButton: (planid: string, value: string) => void;
  flexGrow?: boolean;
  planid: string;
}

const CommenCard: React.FC<props> = ({
  children,
  classes,
  header,
  buttonText,
  selectedText,
  value,
  onClickButton,
  selectedValue,
  planid,
  flexGrow = true,
}) => {
  const isSelected = selectedValue == value;
  return (
    <div className={`${classes} rounded-[10px] bg-white  flex flex-col`}>
      <div className=" bg-dark py-2.5 px-4 rounded-t-[inherit] mb-4">
        <span className="text-lg text-white font-bold">{header}</span>
      </div>
      <div
        className={`px-4 pb-4 flex flex-col gap-4 ${flexGrow && "flex-grow"}`}
      >
        <div className="flex-grow">{children}</div>
        <div>
          <PrimaryButton
            onClick={() => onClickButton(planid, value)}
            text={isSelected ? selectedText || "Selected" : buttonText}
            classes="!py-2 w-full"
          />
        </div>
      </div>
    </div>
  );
};

export default CommenCard;
