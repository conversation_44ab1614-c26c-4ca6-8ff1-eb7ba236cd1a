"use client";
import React, { useEffect } from "react";
import { useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import { z } from "zod";
import { useRouter } from "next/navigation";
import CustomInput from "../reuseable/CustomInput";
import CustomTextarea from "../reuseable/CustomTextArea";
import FormWrapper from "../reuseable/FormWrapper";
import FormHeading from "../reuseable/FormHeading";
import SquareCheckBox from "../reuseable/SquareCheckBox";
import { submitClinicalConditions } from "@/utils/ApisHelperFunction";
import { getDecryptedToken } from "@/app/lib/auth";
import { PatientFileData } from "@/types/types";
import { toast } from "react-toastify";

const disclamer = "*Note above are for the practitioner reference only";

const dentalConditionsLeft = [
  "Crowding",
  "Spacing",
  "Class II Div 1",
  "Class II Div 2",
  "Class III",
  "Open Bite",
  "Anterior Crossbite",
  "Other",
] as const;

const dentalConditionsRight = [
  "Deep Bite",
  "Narrow Arch",
  "Flared Teeth",
  "Overjet",
  "Uneven Smile",
  "Misshapen Teeth",
  "Posterior Crossbite",
] as const;

const clinicalConditionsSchema = z
  .object({
    clinicalConditions: z.array(z.string(), {
      required_error: "Clinical condition is required",
      invalid_type_error: "Clinical condition is required",
    }),
    otherClinicalCondition: z.string().optional(),
    generalNotes: z.string().optional(),
  })
  .superRefine((data, ctx) => {
    if (
      data.clinicalConditions &&
      data.clinicalConditions.includes("Other") &&
      !data.otherClinicalCondition
    ) {
      ctx.addIssue({
        path: ["clinicalConditions"],
        code: z.ZodIssueCode.custom,
        message: "Please add other clinical condition",
      });
    }
    if (data.clinicalConditions && data.clinicalConditions.length == 0) {
      ctx.addIssue({
        path: ["clinicalConditions"],
        code: z.ZodIssueCode.custom,
        message: "Clinical condition is required",
      });
    }
  });

type ClinicalConditionsFormData = z.infer<typeof clinicalConditionsSchema>;

const ClinicalConditions = ({
  patientData,
}: {
  patientData: PatientFileData | null;
}) => {
  const {
    register,
    handleSubmit,
    watch,
    setValue,
    formState: { errors },
  } = useForm<ClinicalConditionsFormData>({
    resolver: zodResolver(clinicalConditionsSchema),
    mode: "onSubmit",
    defaultValues: { clinicalConditions: [] },
  });
  const router = useRouter();

  // Prefill form with patientData
  useEffect(() => {
    if (patientData) {
      // Set clinical conditions from patientData
      if (patientData.clinical_conditions) {
        const clinicalConditionsArray = patientData.clinical_conditions
          .split(", ")
          .map((condition) => condition.trim())
          .filter((condition) => condition.length > 0);

        setValue("clinicalConditions", clinicalConditionsArray);
      }

      // Set general notes if available
      if (patientData.general_notes) {
        setValue("generalNotes", patientData.general_notes);
      }
    }
  }, [patientData, setValue]);

  const onSubmit = async (data: ClinicalConditionsFormData) => {
    console.log("🚀 ~ onSubmit ~ data:", data);
    let clinicalConditions;
    if (data.otherClinicalCondition) {
      clinicalConditions = data.clinicalConditions.filter(
        (e: string) => e !== "Other",
      );
      clinicalConditions = [...clinicalConditions, data.otherClinicalCondition];
    } else {
      clinicalConditions = data.clinicalConditions;
    }

    // Convert array to comma-separated string
    const clinicalConditionsString = clinicalConditions?.join(", ") || "";
    console.log(
      "🚀 ~ onSubmit ~ clinicalConditionsString:",
      clinicalConditionsString,
    );
    setValue("clinicalConditions", clinicalConditions);

    try {
      // Retrieve the patient ID and token from cookies
      const patientId = getDecryptedToken("patientId");
      const token = getDecryptedToken("AccessToken");

      if (!token) {
        return;
      }
      if (!patientId) {
        return;
      }

      const payload = {
        step: "step2",
        id: patientId,
        clinical_conditions: clinicalConditionsString, // Use the string directly
        general_notes: data.generalNotes || "",
      };

      // Call the helper function
      const response = await submitClinicalConditions(token, payload);
      console.log("🚀 ~ onSubmit ~ response:", response);

      if (response) {
        console.log("Clinical conditions submitted successfully:", response);
        router.push("/patient-records");
      } else {
        toast.error("Failed to submit clinical conditions");
      }
    } catch {
      return null;
    }
  };

  const clinicalConditionArray = watch("clinicalConditions");
  useEffect(() => {
    console.log(clinicalConditionArray);
  }, [clinicalConditionArray]);

  return (
    <>
      <FormWrapper
        onSubmit={handleSubmit(onSubmit)}
        classNames="!flex flex-col !gap-2"
        onBack={() => router.back()}
      >
        <div className=" flex flex-col">
          <FormHeading text="Clinical Conditions*" />
          <div className="grid grid-cols-3 gap-2">
            <div className="flex flex-col gap-3 flex-grow">
              {dentalConditionsLeft.map((condition) => (
                <SquareCheckBox
                  key={condition}
                  id={condition}
                  label={condition}
                  value={condition}
                  name="clinicalConditions"
                  register={register("clinicalConditions")}
                  labelClass="!text-gray"
                  className="bg-gray/20 border-0!"
                />
              ))}
            </div>
            <div className="flex flex-col gap-3 flex-grow shrink-0">
              {dentalConditionsRight.map((condition) => (
                <SquareCheckBox
                  key={condition}
                  id={condition}
                  label={condition}
                  name="clinicalConditions"
                  register={register("clinicalConditions")}
                  value={condition}
                  labelClass="!text-gray"
                  className="bg-gray/20 border-0!"
                />
              ))}
              <div>
                {clinicalConditionArray?.includes("Other") && (
                  <CustomInput
                    className=""
                    type="text"
                    register={register("otherClinicalCondition")}
                  />
                )}
              </div>
            </div>
          </div>

          <div className="mt-3">
            {errors.clinicalConditions?.message && (
              <p className="text-red-500 text-sm">
                {errors.clinicalConditions?.message}
              </p>
            )}
          </div>

          {/* <div className='grid grid-cols-2 mt-3'>
          <div className='col-span-1'>
            <RoundRadioButton
              onClick={(e: React.MouseEvent<HTMLInputElement>) => setClinicalCondition((e.target as HTMLInputElement).value)}
              key={"other"}
              id={"other"}
              name={"clinicalConditions"}
              label={"Others"}
              value={"other"}
              register={register}
            />
          </div>
          <div className='col-span-1'>
            {otherComments && <CustomInput className='' type='text' register={register("clinicalConditionsNote")} />}
          </div>

        </div> */}
        </div>

        <div className="">
          <FormHeading text="General Notes" />
          <CustomTextarea
            className="px-4 !py-4"
            error={errors.generalNotes?.message}
            register={register("generalNotes")}
            rows={8}
          />
          <div className="">
            <p className="text-gray">{disclamer}</p>
          </div>
        </div>
      </FormWrapper>
    </>
  );
};

export default ClinicalConditions;
