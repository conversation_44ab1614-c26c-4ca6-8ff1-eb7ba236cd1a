"use client";
import {
  <PERSON><PERSON><PERSON>ile<PERSON><PERSON>,
  PatientFile,
  Refine<PERSON><PERSON><PERSON><PERSON><PERSON>,
  Retainer,
} from "@/types/types";
import buccalLeft from "../../../public/svgs/buccal-left.svg";
import buccalRight from "../../../public/svgs/buccal-right.8f4707a1..svg";
import frontalRepose from "../../../public/svgs/frontal-repose.5f430b49..svg";
import frontalSmiling from "../../../public/svgs/frontal-smiling.6c08f65f..svg";
import labialAnterior from "../../../public/svgs/labial-anterior.9cf4e2c6..svg";
import occlussalLower from "../../../public/svgs/occlussal-lower.3be1bcdf..svg";
import occlussalUpper from "../../../public/svgs/occlussal-upper.cd664940..svg";
import profileRepose from "../../../public/svgs/profile-repose.cf7b4b65..svg";
import socialSmile from "../../../public/svgs/4dGraphyLogo.svg";
import Link from "next/link";
import Image from "next/image";
import { useState } from "react";
export const RecordsTab = ({ data }: { data: PatientFileData }) => {
  const photos = [
    {
      name: "Profile Repose",
      img: data?.profileRepose ? data?.profileRepose : profileRepose,
    },
    {
      name: "Frontal Repose",
      img: data?.frontalRepose ? data?.frontalRepose : frontalRepose,
    },
    {
      name: "Frontal Smiling",
      img: data?.frontalSmiling ? data?.frontalSmiling : frontalSmiling,
    },
    {
      name: "Occlussal Upper",
      img: data?.occlussalUpper ? data?.occlussalUpper : occlussalUpper,
    },
    { name: "Social Smile", img: socialSmile },
    {
      name: "Occlussal Lower",
      img: data?.occlussalLower ? data?.occlussalLower : occlussalLower,
    },
    {
      name: "Buccal Right",
      img: data?.buccalRight ? data?.buccalRight : buccalRight,
    },
    {
      name: "Labial Anterior",
      img: data?.labialAnterior ? data?.labialAnterior : labialAnterior,
    },
    {
      name: "Buccal Left",
      img: data?.buccalLeft ? data?.buccalLeft : buccalLeft,
    },
  ];

  const stlFiles = [
    {
      name: "Upper STL",
      Url: data?.stlFile1 ? data?.stlFile1 : null,
    },
    {
      name: "Lower STL",
      Url: data?.stlFile2 ? data?.stlFile2 : null,
    },
  ];

  const radiographs = [
    {
      name: "Radiograph 1",
      Url: data?.radioGraph1 ? data?.radioGraph1 : null,
    },
    {
      name: "Radiograph 2",
      Url: data?.radioGraph2 ? data?.radioGraph2 : null,
    },
  ];

  const [isAccordionOpen, setIsAccordionOpen] = useState(true);

  // Modal state
  const [openImageModal, setOpenImageModal] = useState<{
    open: boolean;
    url: string | null;
  }>({ open: false, url: null });
  const [openRetainer, setOpenRetainer] = useState(false);
  const [openRefinement, setOpenRefinement] = useState(false);
  const [cBCT, setCBCT] = useState(false);

  // Helper for rendering images
  const renderImage = (img: string | null, name: string) => {
    if (!img) return null;
    return (
      <div className="h-32 w-full relative border rounded mb-2 flex items-center justify-center bg-gray-50 border-gray-200">
        <Image
          src={img}
          alt={name}
          width={1000}
          height={1000}
          className="object-contain max-h-32 max-w-full cursor-pointer"
          onClick={() => setOpenImageModal({ open: true, url: img })}
        />
      </div>
    );
  };

  // Helper for rendering STL download
  const renderDownload = (url: string | null, label = "Download") =>
    url ? (
      <div className="flex items-center p-3 border border-gray-200 rounded-lg">
        <div className="h-10 w-10 relative flex-shrink-0">
          {/* File Icon */}
          <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
            <svg
              className="w-6 h-6 text-primary"
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
              ></path>
            </svg>
          </div>
        </div>
        <div className="ml-3 flex-grow">
          <h4 className="text-sm font-medium text-gray-800">{label}</h4>
        </div>
        {url !== null && (
          <Link href={url} target="_blank" rel="noopener noreferrer">
            <button className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer">
              Download
            </button>
          </Link>
        )}
      </div>
    ) : null;

  const toggleAccordion = () => {
    setIsAccordionOpen(!isAccordionOpen);
  };
  const retainer: Retainer[] = data.retainers;
  const refinementsAligner: RefinementAligner[] = data.refinementsAligner;
  const CbctsFiles: PatientFile[] = data.patientFiles;

  return (
    <div className="p-4 space-y-4">
      {/* Image Modal */}
      {openImageModal.open && openImageModal.url && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/20">
          <div className="bg-white rounded-lg p-4 max-w-md w-full relative">
            <button
              className="absolute top-2 right-2 text-gray-500 hover:text-gray-700"
              onClick={() => setOpenImageModal({ open: false, url: null })}
            >
              &times;
            </button>
            <div className="flex flex-col items-center">
              <Image
                src={openImageModal.url}
                width={1000}
                height={1000}
                alt="..."
                className="max-h-96 max-w-full rounded"
              />
            </div>
          </div>
        </div>
      )}

      {/* Single Accordion for All Records */}
      <div className="border border-gray-200 rounded-xl overflow-hidden">
        <div
          className={`flex justify-between items-center p-4 cursor-pointer ${isAccordionOpen ? "bg-orange-50" : "bg-white"}`}
          onClick={toggleAccordion}
        >
          <h3 className="font-semibold text-gray-800">Patient Records</h3>
          <svg
            className={`w-5 h-5 transition-transform ${isAccordionOpen ? "transform rotate-180" : ""}`}
            fill="none"
            stroke="currentColor"
            viewBox="0 0 24 24"
          >
            <path
              strokeLinecap="round"
              strokeLinejoin="round"
              strokeWidth="2"
              d="M19 9l-7 7-7-7"
            ></path>
          </svg>
        </div>

        {isAccordionOpen && (
          <div className="p-4 bg-white space-y-6">
            {/* Photos Section */}
            <div>
              <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                Photos
              </h4>
              <div className="grid grid-cols-3 gap-4">
                {photos.map((photo, index) => (
                  <div
                    key={index}
                    className="border border-gray-200 rounded-lg overflow-hidden"
                  >
                    <div className="h-40 relative">
                      {typeof photo.img === "string" &&
                      photo.img.startsWith("http") ? (
                        // If img is a URL, use a regular img tag
                        <Image
                          src={photo.img}
                          alt={photo.name}
                          className="object-fill w-full h-full"
                          width={1000}
                          height={1000}
                          style={{ objectFit: "fill" }}
                        />
                      ) : (
                        // Otherwise, use Next.js Image for local imports
                        <Image
                          src={photo.img}
                          alt={photo.name}
                          fill
                          className="object-fill"
                        />
                      )}
                    </div>
                  </div>
                ))}
              </div>
            </div>

            {/* STL Files Section */}
            <div>
              <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                STL Files
              </h4>
              <div className="space-y-3">
                {stlFiles.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center p-3 border border-gray-200 rounded-lg"
                  >
                    <div className="h-10 w-10 relative flex-shrink-0">
                      {/* File Icon */}
                      <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                        <svg
                          className="w-6 h-6 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div className="ml-3 flex-grow">
                      <h4 className="text-sm font-medium text-gray-800">
                        {file.name}
                      </h4>
                    </div>
                    {file.Url !== null && (
                      <Link
                        href={file.Url}
                        target="_blank"
                        rel="noopener noreferrer"
                      >
                        <button className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer">
                          Download
                        </button>
                      </Link>
                    )}
                  </div>
                ))}
              </div>
            </div>

            {/* Radiographs Section */}
            <div>
              <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                Radiographs
              </h4>
              <div className="space-y-3">
                {radiographs.map((file, index) => (
                  <div
                    key={index}
                    className="flex items-center p-3 border border-gray-200 rounded-lg"
                  >
                    <div className="h-10 w-10 relative flex-shrink-0">
                      {/* Radiograph Icon */}
                      <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                        <svg
                          className="w-6 h-6 text-primary"
                          fill="none"
                          stroke="currentColor"
                          viewBox="0 0 24 24"
                        >
                          <path
                            strokeLinecap="round"
                            strokeLinejoin="round"
                            strokeWidth="2"
                            d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                          ></path>
                        </svg>
                      </div>
                    </div>
                    <div className="ml-3 flex-grow">
                      <h4 className="text-sm font-medium text-gray-800">
                        {file.name}
                      </h4>
                    </div>
                    {file.Url && (
                      <button
                        className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer"
                        onClick={() =>
                          setOpenImageModal({ open: true, url: file.Url! })
                        }
                      >
                        View
                      </button>
                    )}
                  </div>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Retainers Accordion */}
      {data.retainers.length > 0 && (
        <div className="border border-gray-200 rounded-xl overflow-hidden mt-4">
          <div
            className={`flex justify-between items-center p-4 cursor-pointer ${openRetainer ? "bg-orange-50" : "bg-white"}`}
            onClick={() => setOpenRetainer(!openRetainer)}
          >
            <h3 className="font-semibold text-gray-800">Retainers</h3>
            <svg
              className={`w-5 h-5 transition-transform ${openRetainer ? "transform rotate-180" : ""}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </div>
          {openRetainer && (
            <div className="p-4 bg-white space-y-6">
              <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                Photos
              </h4>
              {retainer.map((ret, idx) => (
                <div key={ret.id || idx} className="mb-6 pb-4">
                  <div className="grid grid-cols-3 gap-4 mb-2">
                    {renderImage(
                      ret.profile_repose ? ret.profile_repose : profileRepose,
                      "Profile Repose",
                    )}
                    {renderImage(
                      ret.frontal_repose ? ret.frontal_repose : frontalRepose,
                      "Frontal Repose",
                    )}
                    {renderImage(
                      ret.frontal_smiling
                        ? ret.frontal_smiling
                        : frontalSmiling,
                      "Frontal Smiling",
                    )}
                    {renderImage(
                      ret.occlusal_upper ? ret.occlusal_upper : occlussalUpper,
                      "Occlusal Upper",
                    )}
                    {renderImage(
                      ret.occlusal_lower ? ret.occlusal_lower : occlussalLower,
                      "Occlusal Lower",
                    )}
                    {renderImage(
                      ret.buccal_right ? ret.buccal_right : buccalRight,
                      "Buccal Right",
                    )}
                    {renderImage(
                      ret.labial_anterior
                        ? ret.labial_anterior
                        : labialAnterior,
                      "Buccal Left",
                    )}
                    {renderImage(
                      ret.buccal_left ? ret.buccal_left : buccalLeft,
                      "Labial Anterior",
                    )}
                  </div>
                  <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                    STL Files
                  </h4>
                  <div className="space-y-3">
                    {renderDownload(
                      ret.stl_file1 ? ret.stl_file1 : null,
                      "Upper STL",
                    )}
                    {renderDownload(
                      ret.stl_file2 ? ret.stl_file2 : null,
                      "Lower STL",
                    )}
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}

      {/* Refinements Aligner Accordion */}
      {data.refinementsAligner.length > 0 && (
        <div className="border border-gray-200 rounded-xl overflow-hidden mt-4">
          <div
            className={`flex justify-between items-center p-4 cursor-pointer ${openRefinement ? "bg-orange-50" : "bg-white"}`}
            onClick={() => setOpenRefinement(!openRefinement)}
          >
            <h3 className="font-semibold text-gray-800">Refinements Aligner</h3>
            <svg
              className={`w-5 h-5 transition-transform ${openRefinement ? "transform rotate-180" : ""}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </div>
          {openRefinement && (
            <div className="p-4 bg-white space-y-6">
              {refinementsAligner.map((ref, idx) => (
                <div key={ref.id || idx}>
                  <div>
                    <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b text-2xl">
                      Refinement Request {idx + 1}
                    </h4>
                  </div>
                  <div className="mb-6 pb-4">
                    <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                      Photos
                    </h4>
                    <div className="grid grid-cols-3 gap-4 mb-2">
                      {renderImage(
                        ref.profileRepose ? ref.profileRepose : profileRepose,
                        "Profile Repose",
                      )}
                      {renderImage(
                        ref.frontalRepose ? ref.frontalRepose : frontalRepose,
                        "Frontal Repose",
                      )}
                      {renderImage(
                        ref.frontalSmiling
                          ? ref.frontalSmiling
                          : frontalSmiling,
                        "Frontal Smiling",
                      )}
                      {renderImage(
                        ref.occlussalUpper
                          ? ref.occlussalUpper
                          : occlussalUpper,
                        "Occlusal Upper",
                      )}
                      {renderImage(
                        ref.occlussalLower
                          ? ref.occlussalLower
                          : occlussalLower,
                        "Occlusal Lower",
                      )}
                      {renderImage(
                        ref.buccalRight ? ref.buccalRight : buccalRight,
                        "Buccal Right",
                      )}
                      {renderImage(
                        ref.buccalLeft ? ref.buccalLeft : buccalLeft,
                        "Buccal Left",
                      )}
                      {renderImage(
                        ref.labialAnterior
                          ? ref.labialAnterior
                          : labialAnterior,
                        "Labial Anterior",
                      )}
                    </div>
                    <div className="flex gap-4 flex-col">
                      <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                        STL Files
                      </h4>
                      <div className="space-y-3">
                        {renderDownload(
                          ref.upper_impression ? ref.upper_impression : null,
                          "Upper STL",
                        )}
                        {renderDownload(
                          ref.lower_impression ? ref.lower_impression : null,
                          "Lower STL",
                        )}
                      </div>
                    </div>
                    <div className=" mt-2">
                      <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b">
                        Radiographs
                      </h4>

                      <div className="flex items-center p-3 border border-gray-200 rounded-lg">
                        <div className="h-10 w-10 relative flex-shrink-0">
                          {/* Radiograph Icon */}
                          <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                            <svg
                              className="w-6 h-6 text-primary"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              ></path>
                            </svg>
                          </div>
                        </div>
                        <div className="ml-3 flex-grow">
                          <h4 className="text-sm font-medium text-gray-800">
                            Radiograph 1
                          </h4>
                        </div>
                        {ref.radioGraph1 && (
                          <button
                            className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer"
                            onClick={() =>
                              setOpenImageModal({
                                open: true,
                                url: ref.radioGraph1!,
                              })
                            }
                          >
                            View
                          </button>
                        )}
                      </div>
                      <div className="flex items-center p-3 border border-gray-200 rounded-lg">
                        <div className="h-10 w-10 relative flex-shrink-0">
                          {/* Radiograph Icon */}
                          <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                            <svg
                              className="w-6 h-6 text-primary"
                              fill="none"
                              stroke="currentColor"
                              viewBox="0 0 24 24"
                            >
                              <path
                                strokeLinecap="round"
                                strokeLinejoin="round"
                                strokeWidth="2"
                                d="M4 16l4.586-4.586a2 2 0 012.828 0L16 16m-2-2l1.586-1.586a2 2 0 012.828 0L20 14m-6-6h.01M6 20h12a2 2 0 002-2V6a2 2 0 00-2-2H6a2 2 0 00-2 2v12a2 2 0 002 2z"
                              ></path>
                            </svg>
                          </div>
                        </div>
                        <div className="ml-3 flex-grow">
                          <h4 className="text-sm font-medium text-gray-800">
                            Radiograph 2
                          </h4>
                        </div>
                        {ref.radioGraph2 && (
                          <button
                            className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer"
                            onClick={() =>
                              setOpenImageModal({
                                open: true,
                                url: ref.radioGraph2!,
                              })
                            }
                          >
                            View
                          </button>
                        )}
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      Added:{" "}
                      {ref.created_at
                        ? new Date(ref.created_at).toLocaleDateString()
                        : ""}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
      {data.patientFiles.length > 0 && (
        <div className="border border-gray-200 rounded-xl overflow-hidden mt-4">
          <div
            className={`flex justify-between items-center p-4 cursor-pointer ${cBCT ? "bg-orange-50" : "bg-white"}`}
            onClick={() => setCBCT(!cBCT)}
          >
            <h3 className="font-semibold text-gray-800">CBCT Files</h3>
            <svg
              className={`w-5 h-5 transition-transform ${cBCT ? "transform rotate-180" : ""}`}
              fill="none"
              stroke="currentColor"
              viewBox="0 0 24 24"
            >
              <path
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="M19 9l-7 7-7-7"
              ></path>
            </svg>
          </div>
          {cBCT && (
            <div className="p-4 bg-white space-y-6">
              {CbctsFiles.map((pat, idx) => (
                <div key={pat.id || idx}>
                  <div>
                    <h4 className="font-medium text-gray-700 mb-3 pb-2 border-b text-2xl">
                      CBCT {idx + 1}
                    </h4>
                  </div>
                  <div className="mb-6 pb-4">
                    <div className="flex gap-4 flex-col">
                      <div className="space-y-3">
                        <div className="flex items-center p-3 border border-gray-200 rounded-lg">
                          <div className="h-10 w-10 relative flex-shrink-0">
                            {/* File Icon */}
                            <div className="bg-orange-100 rounded-full w-10 h-10 flex items-center justify-center">
                              <svg
                                className="w-6 h-6 text-primary"
                                fill="none"
                                stroke="currentColor"
                                viewBox="0 0 24 24"
                              >
                                <path
                                  strokeLinecap="round"
                                  strokeLinejoin="round"
                                  strokeWidth="2"
                                  d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                                ></path>
                              </svg>
                            </div>
                          </div>
                          <div className="ml-3 flex-grow"></div>
                          {pat.file_name !== null && (
                            <Link
                              href={pat.file_name}
                              target="_blank"
                              rel="noopener noreferrer"
                            >
                              <button className="px-3 py-1 text-xs bg-primary text-white rounded-full cursor-pointer">
                                Download
                              </button>
                            </Link>
                          )}
                        </div>
                      </div>
                    </div>
                    <div className="text-xs text-gray-500 mt-2">
                      Added:{" "}
                      {pat.created_at
                        ? new Date(pat.created_at).toLocaleDateString()
                        : ""}
                    </div>
                  </div>
                </div>
              ))}
            </div>
          )}
        </div>
      )}
    </div>
  );
};
