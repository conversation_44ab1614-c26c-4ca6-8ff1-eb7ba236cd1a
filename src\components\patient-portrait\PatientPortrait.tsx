"use client";
import React, { useState } from "react";
import { useRouter } from "next/navigation";
import { useForm } from "react-hook-form";
import FormWrapper from "../reuseable/FormWrapper";
import RoundRadioButton from "../reuseable/RoundRadioButton";
import Image from "next/image";
import portraitImage from "../../../public/svgs/femailAvatar.svg";
import imageFileIcon from "../../../public/svgs/icons8_image_file_add 1.svg";
import DisableOverlay from "../reuseable/DisableOverlay";
import UploadedRecords from "../reuseable/UploadedRecords";

const PatientPortrait = () => {
  const router = useRouter();
  const [uploadOption, setUploadOption] = useState<"now" | "later">("now");
  const [portrait, setPortrait] = useState<File | null>(null);
  const [error, setError] = useState<string>("");

  const { register, handleSubmit } = useForm<FormData>();

  interface FormData {
    uploadOption: "now" | "later";
  }

  const onSubmit = (data: FormData) => {
    console.log("Form data:", data);

    if (data.uploadOption === "now") {
      if (!portrait) {
        setError("Patient portrait is required");
        return;
      }
      console.log("Uploaded portrait file:", portrait);
    } else {
      // If "later" is selected, clear the portrait and allow navigation
      setPortrait(null);
      setError("");
    }
    localStorage.setItem("patientPortrait", JSON.stringify(data));
    router.push("/scan");
  };

  const handleFileChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target?.files ? e.target.files[0] : null;
    setPortrait(file);
    setError(""); // Clear any previous error
    if (file) {
      console.log("Uploaded file:", file);
    }
  };

  const handleUploadOptionChange = (value: "now" | "later") => {
    setUploadOption(value);
    if (value === "later") {
      setPortrait(null); // Clear the portrait if "later" is selected
      setError(""); // Clear any validation error
    }
  };

  return (
    <>
      <FormWrapper
        classNames="!grid-cols-1"
        onSubmit={handleSubmit(onSubmit)}
        onBack={() => router.back()}
      >
        <div className="col-span-1 flex flex-col flex-grow min-h-[550px]">
          {/* Radio Buttons */}
          <div className="flex gap-32 mb-4">
            <RoundRadioButton
              id="image-now"
              label="Upload patient portrait now"
              value="now"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === "now"}
              labelClass="!text-dark font-medium"
              onClick={(e) =>
                handleUploadOptionChange(
                  (e.target as HTMLInputElement).value as "now",
                )
              }
            />
            <RoundRadioButton
              id="image-later"
              label="Upload patient portrait later"
              value="later"
              name="uploadOption"
              register={register}
              defaultChecked={uploadOption === "later"}
              labelClass="!text-dark font-medium"
              onClick={(e) =>
                handleUploadOptionChange(
                  (e.target as HTMLInputElement).value as "later",
                )
              }
            />
          </div>

          {/* Main Content */}
          <div className="grid !grid-cols-12 !gap-6 flex-grow ">
            {/* Portrait Upload Section */}
            <div className="relative xl:col-span-8 col-span-12 max-h-[700px] min-h-[500px] flex flex-col items-center justify-center gap-3 !bg-primaryLight rounded-[10px]">
              <DisableOverlay
                active={uploadOption === "later"}
                color={"bg-black/40"}
              />
              {portrait && (
                <Image
                  src={URL.createObjectURL(portrait)}
                  alt={`Uploaded portrait`}
                  width={1000}
                  height={1000}
                  className="object-contain w-full h-[80%]"
                />
              )}
              {!portrait && (
                <>
                  <Image src={portraitImage} alt="Cross icon" />
                  <div>
                    <div>
                      <label
                        htmlFor="file-upload"
                        className="px-6 py-3 border border-gray text-sm text-dark rounded-full flex items-center gap-2 cursor-pointer"
                      >
                        <Image
                          src={imageFileIcon}
                          alt="Upload icon"
                          className="w-6 h-6"
                        />
                        <span className="text-gray font-semibold">
                          Upload Images
                        </span>
                      </label>
                      <input
                        id="file-upload"
                        type="file"
                        accept="image/*"
                        onChange={handleFileChange}
                        className="hidden"
                      />
                    </div>
                    {error && (
                      <p className="text-red-500 text-sm mt-2 text-center">
                        {error}
                      </p>
                    )}
                  </div>
                </>
              )}
            </div>

            {/* Uploaded Records Section */}
            <div className="xl:col-span-4 col-span-12 flex flex-col gap-4">
              <UploadedRecords
                withScan={true}
                disabled={uploadOption === "later"}
              />
              <div className="relative rounded-[10px]">
                <DisableOverlay
                  active={uploadOption === "later"}
                  color={"bg-black/40"}
                />
                {portrait && (
                  <Image
                    src={URL.createObjectURL(portrait)}
                    alt={`Uploaded portrait`}
                    width={150}
                    height={150}
                    className="rounded-[10px] object-cover w-full"
                  />
                )}
              </div>
            </div>
          </div>
        </div>
      </FormWrapper>
    </>
  );
};

export default PatientPortrait;
