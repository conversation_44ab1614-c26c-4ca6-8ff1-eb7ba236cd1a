"use client";

import { formatStatus } from "@/utils/helperFunctions";
import Pagination from "./Pagination";
import Image from "next/image";
import { useRouter } from "next/navigation";

interface Patient {
  image?: string;
  name: string;
  id: string;
}

interface TableRow {
  id: string;
  patient: Patient;
  startDate: string;
  treatmentOption: string;
  status: string;
  notes: string;
  daysSinceLastUpdate: number;
  country: string;
}
export interface Column {
  accessor: keyof TableRow;
  label: string;
}
interface PaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void;
}

interface TableComponentProps {
  data: TableRow[];
  columns: Column[];
  pagination: PaginationProps;
}

const TableComponent: React.FC<TableComponentProps> = ({
  data,
  columns,
  pagination,
}) => {
  const navigate = useRouter();

  const handleRowClick = (patientId: string, row: TableRow) => {
    console.log("Patient ID:", patientId);
    console.log("Full row data:", row);
    navigate.push(`/patient-file?id=${patientId}`);
  };

  // const renderStatus = (status: string) => {
  //     const stages: string[] = ['Aligner Shipped', 'Aligner Shipped (Track)', 'Completed'];
  //     const currentStageIndex = stages.indexOf(status) !== -1 ? stages.indexOf(status) : 0; // Default to the first step

  //     return (
  //         <div className="flex flex-col">
  //             {/* Progress Bar */}
  //             <div className="flex items-center w-full">
  //                 {stages.map((stage, idx) => (
  //                     <div key={idx} className="flex items-center">
  //                         {/* Circle */}
  //                         <div
  //                             className={`relative w-4 h-4 rounded-full flex items-center justify-center ${idx <= currentStageIndex
  //                                 ? 'bg-[#EB6309]' // Completed or active stages
  //                                 : 'bg-gray-300' // Incomplete stages
  //                                 }`}
  //                         >
  //                             {/* Tick Icon */}
  //                             {(idx < currentStageIndex || (idx === currentStageIndex && status === 'Completed')) && (
  //                                 <svg
  //                                     xmlns="http://www.w3.org/2000/svg"
  //                                     className="w-3 h-3 text-white"
  //                                     fill="none"
  //                                     viewBox="0 0 24 24"
  //                                     stroke="currentColor"
  //                                     strokeWidth={2}
  //                                 >
  //                                     <path
  //                                         strokeLinecap="round"
  //                                         strokeLinejoin="round"
  //                                         d="M5 13l4 4L19 7"
  //                                     />
  //                                 </svg>
  //                             )}
  //                         </div>
  //                         {/* Line */}
  //                         {idx < stages.length - 1 && (
  //                             <div
  //                                 className={`h-1 w-8 ${idx < currentStageIndex ? 'bg-[#EB6309]' : 'bg-gray-300'
  //                                     }`}
  //                             />
  //                         )}
  //                     </div>
  //                 ))}
  //             </div>
  //             {/* Active Step Label */}
  //             <div className="mt-2 w-fit text-nowrap text-start text-sm font-medium text-orange-500">
  //                 {stages[currentStageIndex]}
  //             </div>
  //         </div>
  //     );
  // };

  const renderPatient = (patient: Patient) => (
    <div className="flex items-center space-x-3 w-32">
      {patient.image ? (
        <Image
          src={patient.image}
          alt="Patient Avatar"
          width={1000}
          height={1000}
          className="w-10 h-10 rounded-full object-cover"
        />
      ) : (
        <Image
          src={`https://ui-avatars.com/api/?name=${patient.name.replace(/ /g, "+")}?background=random?rounded=true`}
          alt="Patient Avatar"
          width={1000}
          height={1000}
          className="w-10 h-10 rounded-full object-cover"
        />
      )}
      <div>
        <div className="text-sm font-medium text-gray-800">{patient.name}</div>
        <div className="text-xs text-gray-500">({patient.id})</div>
      </div>
    </div>
  );

  return (
    <div className="bg-white rounded-lg shadow-sm w-full max-xl:overflow-x-scroll xl:over-hidden">
      <table className="w-full border-collapse">
        <thead>
          <tr className="bg-[#EDEEEE] border-b border-gray-200">
            {columns.map((col) => (
              <th
                key={col.accessor}
                className="px-4 py-3 text-left text-xs font-semibold text-[#2B2B2B] tracking-wider"
              >
                {col.label}
              </th>
            ))}
          </tr>
        </thead>
        <tbody>
          {data.map((row, idx) => (
            <tr
              key={idx}
              className={`border-b cursor-pointer border-gray-200 hover:bg-gray-50 ${
                idx % 2 === 0 ? "bg-white" : "bg-gray-50"
              }`}
              onClick={() => handleRowClick(row.id, row)}
            >
              {columns.map((col) => (
                <td
                  key={col.accessor}
                  className="px-2 py-3 text-sm text-gray-700"
                >
                  {col.accessor === "patient" ? (
                    renderPatient(row[col.accessor])
                  ) : col.accessor === "status" ? (
                    <div>{formatStatus(row[col.accessor])}</div>
                  ) : col.accessor === "notes" ? (
                    <div
                      className="max-w-xs truncate"
                      title={row[col.accessor]}
                    >
                      {row[col.accessor]}
                    </div>
                  ) : col.accessor === "daysSinceLastUpdate" ||
                    col.accessor === "country" ? (
                    <div className="text-center">{row[col.accessor]}</div>
                  ) : (
                    <div
                      className="max-w-32 truncate"
                      title={row[col.accessor]}
                    >
                      {row[col.accessor]}
                    </div>
                  )}
                </td>
              ))}
            </tr>
          ))}
        </tbody>
      </table>
      {/* Pagination Component */}
      <div className="pb-1">
        <Pagination
          currentPage={pagination.currentPage}
          totalItems={pagination.totalItems}
          itemsPerPage={pagination.itemsPerPage}
          totalPages={pagination.totalPages}
          onPageChange={pagination.onPageChange}
          onItemsPerPageChange={pagination.onItemsPerPageChange}
        />
      </div>
    </div>
  );
};

export default TableComponent;
