import Cookies from "js-cookie";
import CryptoJS from "crypto-js";

const SECRET_KEY = process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "fallback-key";
const COOKIE_NAMES = [
  "AccessToken",
  "Email",
  "Username",
  "first_name",
  "last_name",
  "role",
];

export function generateIntegrityHash(): string {
  let combined = "";
  COOKIE_NAMES.forEach((name) => {
    combined += Cookies.get(name) || "";
  });
  return CryptoJS.HmacSHA256(combined, SECRET_KEY).toString();
}

export function storeIntegrityHash() {
  const hash = generateIntegrityHash();
  Cookies.set("SessionIntegrity", hash, { expires: 1, path: "/" });
}

export function verifyIntegrityHash(): boolean {
  try {
    const storedHash = Cookies.get("SessionIntegrity");
    if (!storedHash) {
      console.debug("No integrity hash found");
      return false;
    }

    const currentHash = generateIntegrityHash();
    const isValid = storedHash === currentHash;

    if (!isValid) {
      console.debug("Integrity check failed", {
        stored: storedHash,
        current: currentHash,
      });
    }

    return isValid;
  } catch {
    return false;
  }
}
