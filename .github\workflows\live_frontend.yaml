name: Deploy to frontend
on:
  push:
    branches:
      - main


jobs:
  build:
    runs-on: ubuntu-latest

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3
        with:
          fetch-depth: 0

      - name: Set up Node.js
        uses: actions/setup-node@v3
        with:
          node-version: '18' # Match this with your server version

      - name: Install dependencies
        run: npm install

      - name: Build the project
        run: npm run build

  deploy:
    runs-on: ubuntu-latest
    needs: build
    if: success()

    steps:
      - name: Checkout repository
        uses: actions/checkout@v3

      - name: Setup SSH for deployment
        run: |
          mkdir -p ~/.ssh
          echo "${{ secrets.STAGING_SSH_KEY }}" > ~/.ssh/id_rsa
          chmod 600 ~/.ssh/id_rsa
          ssh-keyscan -H ${{ secrets.STAGING_HOST }} >> ~/.ssh/known_hosts

      - name: Deploy and reload with PM2
        run: |
          ssh -o StrictHostKeyChecking=no -i ~/.ssh/id_rsa ${{ secrets.STAGING_USER }}@${{ secrets.STAGING_HOST }} << 'EOF'
            # Load NVM and add Node to PATH
            export NVM_DIR="$HOME/.nvm"
            [ -s "$NVM_DIR/nvm.sh" ] && \. "$NVM_DIR/nvm.sh"
            [ -s "$NVM_DIR/bash_completion" ] && \. "$NVM_DIR/bash_completion"
            
            cd /root/orthodontic-lab-management-frontend
            git reset --hard
            git clean -fd
            git pull origin main
            
            # Use full path to npm to be safe
            /root/.nvm/versions/node/v24.4.1/bin/npm install
            if /root/.nvm/versions/node/v24.4.1/bin/npm run build; then
              pm2 reload frontend
              pm2 save
              echo "Deployment to frontend successful"
            else
              echo "Build Failed! Please check the code."
              exit 1
            fi
          EOF