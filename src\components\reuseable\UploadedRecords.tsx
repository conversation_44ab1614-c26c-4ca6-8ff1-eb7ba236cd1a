"use client";
import React from "react";
import DisableOverlay from "./DisableOverlay";

interface props {
  withScan?: boolean;
  disabled?: boolean;
  none?: boolean;
}

const UploadedRecords: React.FC<props> = ({ withScan, disabled, none }) => {
  return (
    <div className="!bg-primaryLight p-4 rounded-[10px] flex flex-col gap-2 relative">
      {disabled && <DisableOverlay active={disabled} color={"bg-black/40"} />}
      <div className="flex justify-between items-center">
        <p className="font-semibold text-lg text-dark">Uploaded Records</p>

        <svg
          xmlns="http://www.w3.org/2000/svg"
          width="20"
          height="21"
          viewBox="0 0 20 21"
          fill="none"
        >
          <path
            d="M19.2887 9.89692C19.1293 9.74488 18.9162 9.66212 18.696 9.66674L10.6904 9.66674L10.6904 1.66115C10.6919 1.54965 10.6711 1.43898 10.6291 1.33569C10.5871 1.23239 10.5248 1.13858 10.4458 1.05981C10.3285 0.942779 10.179 0.863381 10.0163 0.831754C9.85366 0.800128 9.68526 0.81771 9.53264 0.882255C9.38002 0.946799 9.2501 1.05538 9.15949 1.19411C9.06887 1.33285 9.02167 1.49545 9.02392 1.66115L9.02392 9.66674L1.01833 9.66674C0.907479 9.66448 0.797293 9.68437 0.694224 9.72523C0.591155 9.76609 0.497274 9.82711 0.418081 9.9047C0.338888 9.9823 0.275975 10.0749 0.233025 10.1771C0.190075 10.2794 0.167953 10.3891 0.167953 10.5C0.167953 10.6109 0.190075 10.7206 0.233025 10.8228C0.275975 10.925 0.338889 11.0177 0.418082 11.0953C0.497274 11.1729 0.591154 11.2339 0.694223 11.2747C0.797292 11.3156 0.907478 11.3355 1.01833 11.3332L9.02392 11.3332L9.02392 19.3388C9.02166 19.4497 9.04155 19.5599 9.08241 19.6629C9.12327 19.766 9.18428 19.8599 9.26188 19.9391C9.33948 20.0183 9.4321 20.0812 9.53431 20.1241C9.63653 20.1671 9.74629 20.1892 9.85716 20.1892C9.96804 20.1892 10.0778 20.1671 10.18 20.1241C10.2822 20.0812 10.3748 20.0183 10.4524 19.9391C10.53 19.8599 10.5911 19.766 10.6319 19.6629C10.6728 19.5599 10.6927 19.4497 10.6904 19.3388V11.3332L18.696 11.3332C18.864 11.3368 19.0291 11.2894 19.1697 11.1975C19.3103 11.1055 19.4198 10.9732 19.4839 10.8179C19.548 10.6626 19.5636 10.4916 19.5288 10.3272C19.494 10.1629 19.4103 10.0129 19.2887 9.89692Z"
            fill="#444443"
          />
        </svg>
      </div>
      {withScan && (
        <div>
          <p className="font-semibold text-lg text-dark">Scans</p>
        </div>
      )}
      {none && (
        <div>
          <p className="text-lg text-dark">None</p>
        </div>
      )}
    </div>
  );
};

export default UploadedRecords;
