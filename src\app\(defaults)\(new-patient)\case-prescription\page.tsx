import { fetchApi, getServerCookies } from "@/api/getapis";
import CasePrescription from "@/components/case-prescription/CasePrescription";
import { PatientFileData } from "@/types/types";
import { API_ROUTES } from "@/utils/ApiRoutes";
import React from "react";

const page = async () => {
  const patientId = await getServerCookies("patientId");
  let patientData: PatientFileData | null = null;

  if (patientId) {
    // Get role to determine correct API endpoint
    const role = await getServerCookies("Role");
    const baseRoute =
      role === "specialist"
        ? API_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST
        : API_ROUTES.PATIENT.GET_PATIENT_BY_ID;

    const patientsArray = await fetchApi(`${baseRoute}/${patientId}`);
    patientData = patientsArray as PatientFileData;
    console.log("🚀 ~ page ~ patientData:", patientData);
  } else {
    console.log("🚀 ~ page ~ patientData:", patientData);
  }

  return (
    <div>
      <CasePrescription patientData={patientData as PatientFileData} />
    </div>
  );
};

export default page;
