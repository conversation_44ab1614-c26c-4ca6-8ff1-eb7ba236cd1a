import { API_ROUTES, API_SERVER_ROUTES } from "@/utils/ApiRoutes"
import type { DoctorProfileApiResponse, ProfileData, UpdateDoctorProfilePayload } from "@/types/types"
import { toast } from "react-toastify"
import {getDecryptedToken} from "@/app/lib/auth"

// ✅ Fetch Doctor Profile
export const fetchDoctorProfile = async (): Promise<DoctorProfileApiResponse> => {
  const url = API_SERVER_ROUTES.PROFILE.GET_PROFILE

  console.log(" fetchDoctorProfile called")
  console.log(" API_SERVER_ROUTES.PROFILE.GET_PROFILE:", url)
  console.log(" Environment variables:", {
    NEXT_PUBLIC_API_BASE_URL: process.env.NEXT_PUBLIC_API_BASE_URL,
    API_BASE_URL: process.env.API_BASE_URL,
  })

  try {
    const token = await getDecryptedToken("AccessToken")
    console.log(" Token retrieved:", token ? "✓ Token exists" : "✗ No token")

    if (!token) {
      console.log(" No token found, returning error response")
      return {
        status: 401,
        success: false,
        profile_image: "",
        email: "",
        last_name: "",
        first_name: "",
        username: "",
        data: {} as ProfileData,
        message: "No authentication token found",
      }
    }

    console.log(" Making fetch request to:", url)
    const response = await fetch(url, {
      method: "GET",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })

    console.log("Response status:", response.status)
    console.log(" Response ok:", response.ok)
    console.log(" Response URL:", response.url)

    const data = await response.json()
    console.log(" Response data:", data)

    if (!response.ok) {
      // Handle non-200 responses (e.g., 401, 500)
      console.error(" API Error:", data)
      return {
        status: response.status,
        success: false,
        profile_image: "",
        email: "",
        last_name: "",
        first_name: "",
        username: "",
        data: {} as ProfileData,
        message: data.message || "Failed to fetch profile",
      }
    }

    console.log(" Profile fetch successful")
    return {
      status: response.status,
      success: true,
      profile_image: data.profile_image || "",
      email: data.email || "",
      last_name: data.last_name || "",
      first_name: data.first_name || "",
      username: data.username || "",
      data: data.data,
      message: data.message || "Profile fetched successfully",
    }
  } catch (error) {
    console.error(" Fetch Error:", error)
    return {
      status: 500,
      success: false,
      profile_image: "",
      email: "",
      last_name: "",
      first_name: "",
      username: "",
      data: {} as ProfileData,
      message: "An unexpected error occurred",
    }
  }
}

// ✅ Update Doctor Profile with Toast Notifications
export const updateDoctorProfile = async (token: string, payload: UpdateDoctorProfilePayload): Promise<boolean> => {
  try {
    if (!token) {
      toast.error("Authorization token is missing")
      return false
    }

    const formData = new FormData()
    formData.append("first_name", payload.first_name)
    formData.append("last_name", payload.last_name)
    formData.append("email", payload.email)
    formData.append("username", payload.username)
    if (payload.profileImage) {
      formData.append("profile_image", payload.profileImage)
    }

    const response = await fetch(API_ROUTES.PROFILE.UPDATE_PROFILE, {
      method: "PUT",
      headers: {
        Authorization: `Bearer ${token}`,
        // Don't set Content-Type for FormData
      },
      body: formData,
    })

    const responseData = await response.json().catch(() => null)

    if (!response.ok) {
      const message = responseData?.message || `Failed to update profile (${response.statusText})`
      toast.error(message)
      return false
    }

    toast.success(responseData?.message || "Profile updated successfully")
    return true
  } catch {
    return false
  }
}
