"use client";
import Image from "next/image";
import Header from "../reuseable/Header";
import { useRouter } from "next/navigation";
import { useState, useEffect } from "react";
import ReplacementModel from "./ReplacementModel";
import RefinementModel from "./RefinementModel";
import RetainerRequestModal from "./RetainerRequestModal";
import UploadCbctModal from "./UploadCbctModal";
import ConversationsTab from "./ConversationsTab";
import { getDecryptedToken, setEncryptedToken } from "@/app/lib/auth";
import ReviewVersionModal from "./ReviewVersionModal";
import { PatientFileData, PatientFileVersion, Specialist } from "@/types/types";
import { RecordsTab } from "./Records";
import { formatStatus } from "@/utils/helperFunctions";
import { API_ROUTES } from "@/utils/ApiRoutes";
import { LoadingOverlay } from "@mantine/core";
import { toast } from "react-toastify";
// import fileIcon from "../../../public/svgs/file-icon.svg";
// import radiographIcon from "../../../public/svgs/xray-icon.svg";

type PatientFileButton = {
  text: string;
  action: () => void;
};

type TabType = "summary" | "records" | "conversations";

export interface PatientFileProps {
  data: PatientFileData | null;
  patientId: string;
  specialist: Specialist;
}

const PatientFile: React.FC<PatientFileProps> = ({
  data,
  patientId,
  specialist,
}) => {
  const [showReplacementModel, setShowReplaceMentModel] = useState(false);
  const [showRefineentModel, setShowRefinementModel] = useState(false);
  const [showRetainerModel, setShowRetainerModel] = useState(false);
  const [showCbctModal, setShowCbctModal] = useState(false);
  const [showLoadMore, setShowLoadMore] = useState(false);

  // Preview modal state
  const [showPreviewModal, setShowPreviewModal] = useState(false);
  const [previewUrl, setPreviewUrl] = useState("");
  const [isPreviewLoading, setIsPreviewLoading] = useState(true);
  const [currentVersionId, setCurrentVersionId] = useState<number | null>(null);
  const [viewdetails, setViewdetails] = useState<Record<number, boolean>>({});
  const [activeTab, setActiveTab] = useState<TabType>("summary");
  const router = useRouter();
  const [role, setRole] = useState<string | null>(null); // ✅ updated
  const [showReviewModal, setShowReviewModal] = useState(false);
  const [hasSpecialist, setHasSpecialist] = useState<
    Specialist | null | undefined
  >(undefined);
  const [reviewVersionId, setReviewVersionId] = useState<number | null>(null);
  const [reasonModalOpen, setReasonModalOpen] = useState(false);
  const [reviewActionType, setReviewActionType] = useState<
    "approve" | "reject" | null
  >(null);
  const [reviewReason, setReviewReason] = useState("");

  useEffect(() => {
    setHasSpecialist(specialist);
  }, [specialist]);

  useEffect(() => {
    const fetchRole = async () => {
      const result = await getDecryptedToken("Role");
      if (result) {
        setRole(result.toLowerCase().trim()); // ✅ clean the role value
      }
    };
    fetchRole();
  }, []);

  // Early return if data is null
  if (!data) {
    return (
      <LoadingOverlay visible>

      </LoadingOverlay>
    );
  }

  const handleClickEditBtn = () => {
    const encryptedPatientId = patientId;
    setEncryptedToken("patientId", encryptedPatientId, false);
    if (data.plan?.type === "aligner") {
      router.push("/patient-data");
    } else {
      router.push("/patient-retainer");
    }
  };

  const allButtons: PatientFileButton[] = [
    {
      text: "Refinement Aligner",
      action: () => {
        setShowRefinementModel(true);
      },
    },
    {
      text: "Aligner Replacement",
      action: () => {
        setShowReplaceMentModel(true);
      },
    },
    {
      text: "4D Graphy Retainers",
      action: () => {
        setShowRetainerModel(true);
      },
    },
    {
      text: "Upload CBCT",
      action: () => {
        setShowCbctModal(true);
      },
    },
  ];

  // Function to toggle specific version details
  const toggleVersionDetails = (versionId: number) => {
    setViewdetails((prev) => ({
      ...prev,
      [versionId]: !prev[versionId],
    }));
  };

  // Add a function to handle opening the preview modal
  const handleOpenPreview = (url: string) => {
    try {
      // Get the encrypted token
      const encryptedToken = getDecryptedToken("AccessToken");
      // Decrypt the token
      const token = encryptedToken ? encryptedToken : null;

      if (token) {
        // Add token as Authorization header
        const urlObj = new URL(url);
        urlObj.searchParams.append("Authorization", `Bearer ${token}`);
        setPreviewUrl(urlObj.toString());
        setShowPreviewModal(true);
      } else {
        router.push("/login");
      }
    } catch {
    } finally {
      setIsPreviewLoading(false);
    }
  };

  const handleReviewAction = (type: "approve" | "reject") => {
    setReviewActionType(type);
    setReviewReason("");
    setReasonModalOpen(true);
  };

  const handleCloseReasonModal = () => {
    setReasonModalOpen(false);
    setReviewActionType(null);
    setReviewReason("");
  };

  const handleSubmitReason = async () => {
    if (!reviewReason.trim()) {
      return;
    }

    try {
      const encryptedToken = getDecryptedToken("AccessToken");
      const token = encryptedToken ? encryptedToken : null;

      if (!token) {
        return;
      }

      // Identify the version whose preview is currently open
      if (!currentVersionId) {
        return;
      }
      const res = await fetch(
        `${API_ROUTES.PATIENT.GET_SHARED_REVIEW}/${currentVersionId}`,
        {
          method: "POST",
          headers: {
            Authorization: `Bearer ${token}`,
            "Content-Type": "application/json",
          },
          body: JSON.stringify({
            action: reviewActionType, // "accept" | "reject"
            reason: reviewReason,
          }),
        },
      );

      const result = await res.json();

      if (result.success) {
        handleCloseReasonModal();
        toast.success(result.message);
        setShowPreviewModal(false);
        router.refresh();
      } else {
        toast.error(result.message);
      }
    } catch (error) {
      const errorMessage =
        typeof error === "object" && error !== null && "message" in error
          ? (error as { message?: string }).message
          : undefined;
      toast.error(errorMessage);
    }
  };

  // Recursive summary renderer for any data shape
  const renderSectionedSummary = (obj: unknown) => {
    if (!obj || typeof obj !== "object") return null;
    return Object.entries(obj).map(([key, value], idx) => (
      <div key={idx} className="pb-2 ">
        {key === "option" || key === "value" || key === "location" ? (
          ""
        ) : (
          <h3 className="font-semibold text-gray-800 capitalize border-b pb-2 mb-2">
            {key.replace(/([A-Z])/g, " $1")}
          </h3>
        )}
        <div className="pl-2">
          {Array.isArray(value) ? (
            value.length === 0 ? (
              <span className="text-gray-400">None</span>
            ) : (
              <ul className="flex gap-3">
                {value.map((item, i) => (
                  <li
                    key={i}
                    className="text-white bg-[#D45A08] px-3 py-1 rounded"
                  >
                    {typeof item === "object"
                      ? renderSectionedSummary(item)
                      : String(item)}
                  </li>
                ))}
              </ul>
            )
          ) : typeof value === "object" && value !== null ? (
            renderSectionedSummary(value)
          ) : (
            <span className="text-gray-700 capitalize">
              {String(value).replace(/([A-Z])/g, " $1") || (
                <span className="text-gray-400">N/A</span>
              )}
            </span>
          )}
        </div>
      </div>
    ));
  };

  const shouldShowSupplementalOptions = (() => {
    // Add null check for data.versions
    if (!data?.versions || data.versions.length === 0) return false;

    const latest = data.versions.find((v) => v.is_latest_version === true);
    if (!latest) return false;
    const allowedStatuses = [
      "approved_by_specialist",
      "rejected_by_specialist",
      "sent_by_specialist",
      "approved_by_doctor",
      "rejected_by_doctor",
      "accepted",
      "in_working",
    ];
    return allowedStatuses.includes(latest.status) && role !== "specialist";
  })();

  const shouldShowEditBtn = (() => {
    // Add null check for data.versions
    if (!data?.versions || data.versions.length === 0) return true;

    const latest = data.versions.find((v) => v.is_latest_version === true);
    if (!latest) return false;
    const allowedStatuses = ["rejected_by_specialist"];
    return allowedStatuses.includes(latest.status) && role !== "specialist";
  })();

  return (
    <div className="p-4 min-h-screen flex flex-col">
      <div className="mb-3">
        <Header onSearchChange={() => { }} searchValue={""} />
      </div>

      {showReplacementModel && (
        <ReplacementModel
          onClose={() => setShowReplaceMentModel(false)}
          patientId={patientId}
        />
      )}
      {showRefineentModel && (
        <RefinementModel
          onClose={() => setShowRefinementModel(false)}
          patientId={patientId}
        />
      )}
      {showRetainerModel && (
        <RetainerRequestModal
          onClose={() => setShowRetainerModel(false)}
          patientId={patientId}
          patientData={data}
        />
      )}
      {showCbctModal && (
        <UploadCbctModal
          onClose={() => setShowCbctModal(false)}
          patientId={patientId}
        />
      )}
      {showReviewModal && reviewVersionId !== null && (
        <ReviewVersionModal
          versionId={reviewVersionId}
          onClose={() => {
            setShowReviewModal(false);
            setReviewVersionId(null);
          }}
          onReviewed={() => {
            // Optionally, refetch patient data to update the UI
            setShowReviewModal(false);
            setReviewVersionId(null);
          }}
        />
      )}
      <div className="flex justify-between items-center mb-4">
        <div className="text-[28px] text-[#444443] font-semibold">
          Patient File
        </div>
        {shouldShowEditBtn && (
          <div className="flex gap-2">
            <button
              type="button"
              onClick={() => handleClickEditBtn()}
              className={`flex items-center justify-center gap-2 !py-2 !px-8 !min-w-0 cursor-pointer rounded-full bg-primary hover:bg-[#D45A08] transition`}
            >
              <span className="font-semibold text-lg text-white">Edit</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M19.3287 9.26799L20.5487 8.048C21.8157 6.781 21.8157 4.719 20.5487 3.4515C19.9352 2.8385 19.1197 2.5015 18.2502 2.5015C17.3807 2.5015 16.5647 2.839 15.9517 3.452L14.7322 4.6715L19.3287 9.26799ZM13.6717 5.732L4.63723 14.7665C4.44473 14.959 4.29823 15.1965 4.21323 15.454L2.53823 20.5145C2.44873 20.7835 2.51923 21.08 2.71973 21.2805C2.86323 21.4235 3.05423 21.5 3.25023 21.5C3.32923 21.5 3.40873 21.4875 3.48623 21.462L8.54523 19.7865C8.80373 19.7015 9.04173 19.555 9.23423 19.362L18.2682 10.328L13.6717 5.732Z"
                  fill="white"
                />
              </svg>
            </button>
            <button
              type="button"
              className={`flex items-center justify-center gap-2 !py-2 !px-8 !min-w-0 cursor-pointer rounded-full bg-primary hover:bg-[#D45A08] transition`}
            >
              <span className="font-semibold text-lg text-white">Archive</span>
              <svg
                xmlns="http://www.w3.org/2000/svg"
                width="24"
                height="24"
                viewBox="0 0 24 24"
                fill="none"
              >
                <path
                  d="M6.25 2C5.0075 2 4 3.0075 4 4.25V19.75C4 20.9925 5.0075 22 6.25 22H17.75C18.9925 22 20 20.9925 20 19.75V4.25C20 3.0075 18.9925 2 17.75 2H14.5V2.75C14.5 3.1645 14.164 3.5 13.75 3.5H10.25C9.836 3.5 9.5 3.1645 9.5 2.75V2H6.25ZM10.25 4.5H13.75C14.164 4.5 14.5 4.8355 14.5 5.25C14.5 5.6645 14.164 6 13.75 6H10.25C9.836 6 9.5 5.6645 9.5 5.25C9.5 4.8355 9.836 4.5 10.25 4.5ZM10.25 7H13.75C14.164 7 14.5 7.3355 14.5 7.75C14.5 8.1645 14.164 8.5 13.75 8.5H10.25C9.836 8.5 9.5 8.1645 9.5 7.75C9.5 7.3355 9.836 7 10.25 7ZM10.25 9.5H13.75C14.164 9.5 14.5 9.8355 14.5 10.25C14.5 10.6645 14.164 11 13.75 11H10.25C9.836 11 9.5 10.6645 9.5 10.25C9.5 9.8355 9.836 9.5 10.25 9.5ZM10.7744 12H13.2256C13.3991 12 13.5594 12.0874 13.6504 12.2354C14.0299 12.8529 15 14.57 15 16C15 17.6545 13.6545 19 12 19C10.3455 19 9 17.6545 9 16C9 14.57 9.97011 12.8529 10.3496 12.2354C10.4406 12.0874 10.6009 12 10.7744 12ZM12 15C11.7348 15 11.4804 15.1054 11.2929 15.2929C11.1054 15.4804 11 15.7348 11 16C11 16.2652 11.1054 16.5196 11.2929 16.7071C11.4804 16.8946 11.7348 17 12 17C12.2652 17 12.5196 16.8946 12.7071 16.7071C12.8946 16.5196 13 16.2652 13 16C13 15.7348 12.8946 15.4804 12.7071 15.2929C12.5196 15.1054 12.2652 15 12 15Z"
                  fill="white"
                />
              </svg>
            </button>
          </div>
        )}
      </div>

      <div className="mb-4 border-b border-gray-200">
        <nav className="flex space-x-6" aria-label="Tabs">
          <button
            onClick={() => setActiveTab("summary")}
            className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === "summary"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
          >
            Summary
          </button>
          <button
            onClick={() => setActiveTab("records")}
            className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === "records"
              ? "border-primary text-primary"
              : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
              }`}
          >
            Records
          </button>
          {/* Only show Conversations tab if hasSpecialist === true and role is not employee */}
          {hasSpecialist && role !== "employee" && (
            <button
              onClick={() => setActiveTab("conversations")}
              className={`py-3 px-1 border-b-2 font-medium text-sm ${activeTab === "conversations"
                ? "border-primary text-primary"
                : "border-transparent text-gray-500 hover:text-gray-700 hover:border-gray-300"
                }`}
            >
              Conversations
            </button>
          )}
        </nav>
      </div>

      <div className="bg-white rounded-[16px] p-3 grid grid-cols-1 lg:grid-cols-3 gap-6 flex-grow">
        {/* Main Content Area - Changes based on active tab */}
        <div className="col-span-2 space-y-6 flex flex-col h-full ">
          {activeTab === "summary" && (
            <div className="p-6 rounded-xl">
              {/* Patient Info */}
              <div className="flex items-center gap-4">
                <Image
                  src={
                    data.profileRepose ||
                    `https://ui-avatars.com/api/?name=${data.first_name}+${data.last_name}`
                  }
                  alt="profile"
                  width={60}
                  height={60}
                  className="rounded-full"
                />
                <div>
                  <p className="text-sm text-gray-500">
                    Name:{" "}
                    <strong>
                      {" "}
                      {data.first_name} {data.last_name}{" "}
                    </strong>
                  </p>
                  <p className="text-sm text-gray-500">
                    Date of Birth:
                    <strong>{new Date(data.dob).toLocaleDateString()}</strong>
                  </p>
                  <p className="text-sm text-gray-500">
                    Bill to Office:{" "}
                    <strong>
                      {data.billToOffice?.clinic_name +
                        ", " +
                        data.billToOffice?.street_address +
                        ", " +
                        data.billToOffice?.city +
                        ", " +
                        data.billToOffice?.postal_code || "N/A"}
                    </strong>
                  </p>
                </div>
                <div className="ml-auto text-sm text-gray-500 space-y-1">
                  <p>
                    Patient: #<strong>{data.uuid}</strong>
                  </p>
                  <p>
                    Ship to Office:{" "}
                    <strong>
                      {data.shipToOffice?.clinic_name +
                        ", " +
                        data.shipToOffice?.street_address +
                        ", " +
                        data.shipToOffice?.city +
                        ", " +
                        data.shipToOffice?.postal_code || "N/A"}
                    </strong>
                  </p>
                </div>
              </div>

              {/* Notes */}
              <div className="mt-6">
                <label className="block text-sm font-medium text-gray-700 mb-1">
                  Notes
                </label>
                <textarea
                  className="w-full border border-gray-300 rounded-md p-2 text-sm outline-0"
                  disabled
                  placeholder="Add a note..."
                  defaultValue={data.general_notes || ""}
                />
              </div>

              {/* Treatment Info */}
              <div className="mt-4 text-sm text-gray-700 space-y-2">
                <p>
                  <span className="font-semibold">Treatment Option:</span>{" "}
                  <span className="text-orange-500 cursor-pointer">
                    {data.plan?.name}
                  </span>
                </p>
                <p>
                  <span className="font-semibold">
                    Treatment Expiration Date:
                  </span>{" "}
                  {data.plan?.expiration_date
                    ? new Date(data.plan.expiration_date).toLocaleDateString()
                    : ""}
                </p>
              </div>
              <div>
                <button
                  className="text-md font-semibold my-2 text-primary underline cursor-pointer"
                  onClick={() => setShowLoadMore((prev) => !prev)}
                >
                  {showLoadMore ? "Hide" : "Load More"}
                </button>
                <div
                  className={`transition-all duration-300 overflow-hidden ${showLoadMore
                    ? "max-h-[2000px] opacity-100 mt-2"
                    : "max-h-0 opacity-0"
                    }`}
                  style={{ willChange: "max-height, opacity" }}
                >
                  <div className="bg-gray-50 rounded p-4">
                    {renderSectionedSummary(data.data)}
                  </div>
                </div>
              </div>

              {/* versions */}
              <div>
                <h2 className="text-md font-semibold my-2">Current Status</h2>
              </div>
              {data.versions
                .sort(
                  (a, b) =>
                    new Date(b.created_at).getTime() -
                    new Date(a.created_at).getTime(),
                )
                .map((items: PatientFileVersion, index: number) => {
                  return (
                    <div
                      className="mt-2 p-2 rounded-[8px] bg-[#EDEEEE]"
                      key={index}
                    >
                      <div className="flex justify-between items-center">
                        <div className="flex items-center gap-1 ms-1">
                          <div>{items.title}</div>
                          <div className="text-sm text-gray-700">{`(${formatStatus(items.status)})`}</div>
                          {/* Show doctor or specialist name based on status and data */}
                          {items.status === "approved_by_doctor" &&
                            data.doctor_name && (
                              <div className="text-sm text-gray-700 font-bold">
                                {data.doctor_name}
                              </div>
                            )}
                          {items.status === "approved_by_specialist" && (
                            <div className="text-sm text-gray-700 font-bold">
                              {data.specialist_name}
                            </div>
                          )}
                        </div>
                        <div className="flex gap-2 me-1 items-center">
                          <div className="flex justify-center items-center gap-2">
                            {items.shared_link !== null &&
                              role !== "specialist" && (
                                <button
                                  type="button"
                                  className="px-6 py-2 bg-orange-500 text-white text-sm rounded-full cursor-pointer"
                                  onClick={() => {
                                    setCurrentVersionId(items.id);
                                    handleOpenPreview(items.shared_link);
                                  }}
                                >
                                  4d Graphy Preview
                                </button>
                              )}
                          </div>

                          {items.status !== "sent_by_doctor" &&
                            items.status !== "cancelled_by_admin" && (
                              <button
                                className="px-6 py-2 bg-primary text-white text-sm rounded-full cursor-pointer"
                                onClick={() => toggleVersionDetails(items.id)}
                              >
                                {viewdetails[items.id]
                                  ? "Hide Details"
                                  : "View Details"}
                              </button>
                            )}
                          {items.status == "sent_by_doctor" &&
                            role == "specialist" && (
                              <button
                                onClick={() => {
                                  setReviewVersionId(items.id);
                                  setShowReviewModal(true);
                                }}
                                className="px-6 py-2 bg-primary text-white text-sm rounded-full cursor-pointer"
                              >
                                Review Version
                              </button>
                            )}
                        </div>
                      </div>
                      <div
                        className={`bg-white rounded-[8px] p-4 mt-2 ${viewdetails[items.id] ? "block" : "hidden"}`}
                      >
                        <div>
                          {items.upper_steps !== null && (
                            <div className="py-1 flex gap-2 items-center">
                              <div>Upper STL:</div>
                              <div>
                                {items.upper_steps === null
                                  ? " No Data Found"
                                  : items.upper_steps}
                              </div>
                            </div>
                          )}
                          {items.lower_steps !== null && (
                            <div className="py-1 flex gap-2 items-center">
                              <div>Lower STL:</div>
                              <div>
                                {items.lower_steps === null
                                  ? " No Data Found"
                                  : items.lower_steps}
                              </div>
                            </div>
                          )}
                        </div>
                        <div>
                          {items.rejection_reason !== null && (
                            <div className="py-1 flex gap-2 items-center">
                              <div>Rejection Reason:</div>
                              <div>
                                {items.rejection_reason === null
                                  ? " No Data Found"
                                  : items.rejection_reason}
                              </div>
                            </div>
                          )}
                          {items.approval_reason !== null && (
                            <div className="py-1 flex gap-2 items-center">
                              <div>Approval Reason:</div>
                              <div>
                                {items.approval_reason === null
                                  ? " No Data Found"
                                  : items.approval_reason}
                              </div>
                            </div>
                          )}
                          {items.shared_link !== null && (
                            <div className="py-1 flex gap-2 items-center">
                              <div className="whitespace-nowrap">
                                Shared Link:
                              </div>
                              <a
                                href={items.shared_link}
                                target="_blank"
                                rel="noopener noreferrer"
                                className="text-blue-600 underline break-all"
                                title={items.shared_link}
                              >
                                {items.shared_link}
                              </a>
                            </div>
                          )}
                        </div>
                      </div>
                    </div>
                  );
                })}
            </div>
          )}

          {activeTab === "records" && <RecordsTab data={data} />}

          {/* Only show Conversations content if hasSpecialist === true and role is not employee */}
          {activeTab === "conversations" &&
            hasSpecialist &&
            role !== "employee" && (
              <div className="flex flex-col flex-grow  ">
                <ConversationsTab
                  currentUserId={Number(data.doctor_id)}
                  patientId={Number(data.id)}
                  doctorsId={Number(data.doctor_id)}
                />
              </div>
            )}
        </div>

        {/* Right Sticky Accordions - Always visible regardless of tab */}
        {shouldShowSupplementalOptions && (
          <div className="sticky top-6 h-fit">
            <div className="bg-[#FFF6F0] p-4 rounded-xl shadow-lg">
              <div className="flex items-center justify-between py-2">
                <p className="text-sm font-semibold text-gray-700">
                  Supplemental order options
                </p>
              </div>
              <div className="pb-4 grid grid-cols-2 gap-2">
                {allButtons.map((item, idx) => (
                  <button
                    onClick={() => item.action()}
                    key={idx}
                    className="w-full text-center cursor-pointer text-xs text-gray-600 border-[#99999966] border-solid border-[1px] rounded-md py-5 px-3"
                  >
                    {item.text}
                  </button>
                ))}
              </div>
            </div>
          </div>
        )}
      </div>

      {/* Add the Preview Modal */}
      {showPreviewModal && (
        <div className="fixed inset-0 z-50 flex items-center justify-center bg-black/70">
          <div className="relative w-[90%] h-[90%] bg-white rounded-lg shadow-xl overflow-hidden">
            {/* Modal header */}
            <div className="flex justify-between items-center p-4 border-b">
              <h2 className="text-xl font-semibold">4D Graphy Preview</h2>
              <button
                title="Close Preview"
                onClick={() => setShowPreviewModal(false)}
                className="text-gray-500 hover:text-gray-700"
              >
                <svg
                  xmlns="http://www.w3.org/2000/svg"
                  width="24"
                  height="24"
                  viewBox="0 0 24 24"
                  fill="none"
                  stroke="currentColor"
                  strokeWidth="2"
                  strokeLinecap="round"
                  strokeLinejoin="round"
                >
                  <line x1="18" y1="6" x2="6" y2="18"></line>
                  <line x1="6" y1="6" x2="18" y2="18"></line>
                </svg>
              </button>
            </div>

            {/* Modal body */}
            <div className="w-full h-[calc(100%-4rem)]">
              {isPreviewLoading ? (
                <div className="flex items-center justify-center h-full">
                  <p>Loading preview...</p>
                </div>
              ) : previewUrl ? (
                <iframe
                  src={previewUrl}
                  width="100%"
                  height="100%"
                  style={{ border: "none" }}
                  title="NemoCloud Viewer"
                  sandbox="allow-same-origin allow-scripts allow-popups allow-forms allow-credentials"
                  allow="fullscreen"
                  referrerPolicy="origin"
                />
              ) : (
                <div className="flex items-center justify-center h-full">
                  <p>Failed to load preview</p>
                </div>
              )}
            </div>

            {/* Action Buttons */}
            {/* Accept / Reject buttons – HIDE when already reviewed */}
            <div className="absolute top-3 right-16 flex gap-4 ">
              {(() => {
                const currentVersion = data.versions.find((v) => v.id === currentVersionId);
                const isDoctor = role === "doctor";
                return (
                  currentVersion &&
                  isDoctor &&
                  currentVersion.status !== "approved_by_doctor" &&
                  currentVersion.status !== "rejected_by_doctor" && (
                    <>
                      <button
                        className="px-4 py-2 bg-[#d45a08] text-white rounded-full hover:bg-[#ffe5d4] hover:text-[#d45a08] cursor-pointer"
                        onClick={() => handleReviewAction("approve")}
                      >
                        Accept
                      </button>
                      <button
                        className="px-4 py-2 bg-[#cc0000] text-white rounded-full hover:bg-[#ffe5e5] hover:text-[#cc0000] cursor-pointer"
                        onClick={() => handleReviewAction("reject")}
                      >
                        Reject
                      </button>
                    </>
                  )
                );
              })()}
            </div>
          </div>
        </div>
      )}

      {reasonModalOpen && (
        <div className="fixed inset-0 flex items-center justify-center bg-black/70 z-[60]">
          <div
            className="bg-white rounded-lg shadow-lg p-6 w-[500px]"
            onClick={(e) => e.stopPropagation()}
          >
            <h2 className="text-lg font-semibold mb-4">
              {reviewActionType === "approve" ? "Approval" : "Rejection"} Reason
            </h2>
            <textarea
              className="w-full border rounded p-2 mb-4 min-h-[100px]"
              placeholder={`Enter reason for ${reviewActionType === "approve" ? "approval" : "rejection"
                }...`}
              value={reviewReason}
              onChange={(e) => setReviewReason(e.target.value)}
            />
            <div className="flex justify-end gap-2">
              <button
                className="px-4 py-2 bg-gray text-white rounded-full hover:bg-[#ffe5d4] hover:text-[#d45a08]  hover:border-red-500  cursor-pointer"
                onClick={handleCloseReasonModal}
              >
                Cancel
              </button>
              <button
                className="px-4 py-2 bg-[#d45a08] text-white rounded-full hover:bg-[#ffe5d4] hover:text-[#d45a08] cursor-pointer"
                onClick={handleSubmitReason}
              >
                Submit
              </button>
            </div>
          </div>
        </div>
      )}
    </div>
  );
};

export default PatientFile;