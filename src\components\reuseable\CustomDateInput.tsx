import React, { useRef, useEffect, useState } from "react";
import { UseFormRegisterReturn } from "react-hook-form";
import flatpickr from "flatpickr";
import "flatpickr/dist/themes/material_green.css";

interface CustomDateInputProps {
  label?: string;
  id?: string;
  name: string;
  value: Date | null;
  register: UseFormRegisterReturn;
  className?: string;
  minDate?: Date;
  maxDate?: Date;
  disabled?: boolean;
  placeholderText?: string;
}

const CustomDateInput: React.FC<CustomDateInputProps> = ({
  label,
  id,
  name,
  value,
  register,
  className = "",
  minDate,
  maxDate,
  disabled = false,
  placeholderText = "Select a date (YYYY-MM-DD)",
}) => {
  const inputId = id || name;
  const inputRef = useRef<HTMLInputElement>(null);
  const fpRef = useRef<flatpickr.Instance | null>(null);
  const [inputValue, setInputValue] = useState(value ? formatDate(value) : "");

  // Helper to format date as Y-m-d
  function formatDate(date: Date) {
    return date.toISOString().slice(0, 10);
  }

  // Helper to format a numeric string as YYYY-MM-DD
  function formatDateString(input: string): string {
    // Remove any non-numeric characters
    const digitsOnly = input.replace(/\D/g, "");

    // If we have 8 digits, format as YYYY-MM-DD
    if (digitsOnly.length === 8) {
      const year = digitsOnly.substring(0, 4);
      const month = digitsOnly.substring(4, 6);
      const day = digitsOnly.substring(6, 8);
      return `${year}-${month}-${day}`;
    }

    return input;
  }

  // Helper to check if a date string is valid
  function isValidDate(dateString: string): boolean {
    const date = new Date(dateString);
    return !isNaN(date.getTime());
  }

  useEffect(() => {
    if (!inputRef.current) return;
    fpRef.current = flatpickr(inputRef.current, {
      dateFormat: "Y-m-d",
      minDate: minDate,
      maxDate: maxDate,
      defaultDate: value || undefined,
      allowInput: true,
      onChange: function (selectedDates) {
        setInputValue(selectedDates[0] ? formatDate(selectedDates[0]) : "");
        register.onChange({
          target: {
            name: name,
            value: selectedDates[0] || null,
          },
          type: "change",
        });
      },
      onClose: function (selectedDates) {
        register.onBlur({
          target: {
            name: name,
            value: selectedDates[0] || null,
          },
          type: "blur",
        });
      },
      disableMobile: true,
    });

    return () => {
      fpRef.current?.destroy();
    };
  }, [minDate, maxDate, value, register, name]);

  // Update inputValue if value prop changes from outside
  useEffect(() => {
    setInputValue(value ? formatDate(value) : "");
  }, [value]);

  return (
    <div className="flex flex-col col-span-1">
      {label && (
        <label htmlFor={inputId} className="text-sm font-medium text-gray-700">
          {label}
        </label>
      )}
      <input
        ref={inputRef}
        id={inputId}
        type="text"
        inputMode="numeric"
        placeholder={placeholderText}
        className={`w-full px-4 py-3 border border-gray-300 rounded-full focus:outline-none ${className} cursor-pointer`}
        disabled={disabled}
        value={inputValue}
        onChange={(e) => {
          let val = e.target.value;

          // Check if input is a continuous string of digits (like 19901220)
          if (/^\d+$/.test(val) && val.length === 8) {
            // Format as YYYY-MM-DD
            val = formatDateString(val);
          }

          setInputValue(val);

          // Update Flatpickr if the value is a valid date format
          if (
            /^\d{4}-\d{2}-\d{2}$/.test(val) &&
            isValidDate(val) &&
            fpRef.current
          ) {
            const date = new Date(val);
            fpRef.current.setDate(date, true);
          }
        }}
        onBlur={(e) => {
          // On blur, try to format the input if it looks like a date
          let val = e.target.value;

          // Handle YYYYMMDD format (8 continuous digits)
          if (/^\d{8}$/.test(val)) {
            val = formatDateString(val);
            setInputValue(val);

            if (isValidDate(val) && fpRef.current) {
              const date = new Date(val);
              fpRef.current.setDate(date, true);
            }
          }
        }}
      />
    </div>
  );
};

export default CustomDateInput;
