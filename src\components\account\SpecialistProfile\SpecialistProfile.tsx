"use client"

import type React from "react"
import { useState, useEffect } from "react"
import { toast } from "react-toastify"
import {getDecryptedToken, setEncryptedToken} from "@/app/lib/auth"
import { updateSpecialistProfile, deleteSpecialistProfileImage } from "@/api/account/specialist"

import Image from "next/image"
import CryptoJS from "crypto-js"
import type { ProfileData, SpecialistProfileApiResponse } from "@/types/types"
import { changePassword, fetchDoctorProfile } from "@/utils/ApisHelperFunction"

const SECRET_KEY = "QkNw7X+dzf1qvOYY6HgLZ9uK8bEN9kZLb8S8FZGhRZc"

export function encryptTableName(tableName: string): string {
  return CryptoJS.AES.encrypt(tableName, SECRET_KEY).toString()
}

interface SpecialistProfileProps {
  initialData?: SpecialistProfileApiResponse | null
  error?: string | null
}

const validatePassword = (password: string) => {
  const errors: string[] = []

  if (password.length < 8) {
    errors.push("Password must be at least 8 characters long")
  }

  if (!/[A-Z]/.test(password)) {
    errors.push("Password must contain at least one uppercase letter")
  }

  if (!/[0-9]/.test(password)) {
    errors.push("Password must contain at least one number")
  }

  if (!/[!@#$%^&*()_+\-=[\]{};':"\\|,.<>/?]/.test(password)) {
    errors.push("Password must contain at least one special character")
  }

  return errors
}

const SpecialistProfile: React.FC<SpecialistProfileProps> = ({ initialData, error: serverError }) => {
  const [username, setUsername] = useState("")
  const [firstName, setFirstName] = useState("")
  const [lastName, setLastName] = useState("")
  const [password] = useState("*************")
  const [email, setEmail] = useState("")
  const [isEditingUsername, setIsEditingUsername] = useState(false)
  const [isEditingFirstName, setIsEditingFirstName] = useState(false)
  const [isEditingLastName, setIsEditingLastName] = useState(false)
  const [isEditingEmail, setIsEditingEmail] = useState(false)
  const [newUsername, setNewUsername] = useState("")
  const [newFirstName, setNewFirstName] = useState("")
  const [newLastName, setNewLastName] = useState("")
  const [newEmail, setNewEmail] = useState("")
  const [profileLoading, setProfileLoading] = useState(!initialData)
  const [profileError, setProfileError] = useState<string | null>(serverError || null)
  const [updateLoading, setUpdateLoading] = useState(false)
  const [profileImage, setProfileImage] = useState<string>("")
  const [selectedImage, setSelectedImage] = useState<File | null>(null)
  const [imagePreview, setImagePreview] = useState<string>("")
  const id = Number(getDecryptedToken("userid"))
  const [userId, setUserId] = useState<number | null>(id)
  const [isEditingPassword, setIsEditingPassword] = useState(false)
  const [currentPassword, setCurrentPassword] = useState("")
  const [newPassword, setNewPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")

  const [newPasswordErrors, setNewPasswordErrors] = useState<string[]>([])
  const [confirmPasswordErrors, setConfirmPasswordErrors] = useState<string[]>([])
  const [passwordsMatch, setPasswordsMatch] = useState(true)
  const [passwordSameAsOld, setPasswordSameAsOld] = useState(false)

  const fetchProfile = async () => {
    // If we have server data, use it first
    if (initialData) {
      setUsername(initialData.username || "")
      setFirstName(initialData.first_name || "")
      setLastName(initialData.last_name || "")
      setEmail(initialData.email || "")
      setProfileImage(initialData.profile_image || "")
      setImagePreview("")
      setProfileLoading(false)
      return
    }

    // Fallback to client-side fetch if no server data
    setProfileLoading(true)
    setProfileError(null)
    setUserId(Number(getDecryptedToken("userid")))
    const token = getDecryptedToken("AccessToken")
    if (token) {
      const profileResponse: ProfileData | null = await fetchDoctorProfile(token)
      if (profileResponse) {
        setUsername(profileResponse.username || "")
        setFirstName(profileResponse.first_name || "")
        setLastName(profileResponse.last_name || "")
        setEmail(profileResponse.email || "")
        setProfileImage(profileResponse.profile_image || "")
        setImagePreview("")
      } else {
        setProfileError("Failed to fetch specialist profile.")
      }
      setProfileLoading(false)
    }
  }

  console.log("userid", userId)
  useEffect(() => {
    fetchProfile()
  }, [initialData])

  const handleChangeField = (
    isEditing: boolean,
    setEditing: React.Dispatch<React.SetStateAction<boolean>>,
    value: string,
    setValue: React.Dispatch<React.SetStateAction<string>>,
    newValue: string,
    setNewValue: React.Dispatch<React.SetStateAction<string>>,
  ) => {
    if (isEditing) {
      if (newValue.trim()) setValue(newValue)
      setEditing(false)
    } else {
      setNewValue(value)
      setEditing(true)
    }
  }

  const handleImageUpload = (e: React.ChangeEvent<HTMLInputElement>) => {
    const file = e.target.files?.[0]
    if (file) {
      // Validate file type
      const validTypes = ["image/jpeg", "image/jpg", "image/png", "image/gif", "image/webp"]
      if (!validTypes.includes(file.type)) {
        toast.error("Please select a valid image file (JPEG, PNG, GIF, WebP)")
        return
      }

      // Validate file size (5MB limit)
      if (file.size > 5 * 1024 * 1024) {
        toast.error("Image size should be less than 5MB")
        return
      }

      setSelectedImage(file)

      // Create preview URL
      const reader = new FileReader()
      reader.onload = (event) => {
        setImagePreview(event.target?.result as string)
      }
      reader.readAsDataURL(file)
    }
  }

  const removeImage = async () => {
    if (profileImage && userId) {
      

      // Extract file key from profile image URL
      let fileKey = profileImage

      // If it's a full URL, extract just the path after the domain
      if (profileImage.startsWith("http")) {
        try {
          const url = new URL(profileImage)
          fileKey = url.pathname.substring(1) // Remove leading slash
        } catch (error) {
          console.log("[v0] Error parsing URL, using original:", error)
          // Fallback: try to extract everything after the last domain part
          const parts = profileImage.split("/")
          const domainIndex = parts.findIndex(
            (part) => part.includes(".com") || part.includes(".net") || part.includes(".org"),
          )
          if (domainIndex !== -1 && domainIndex < parts.length - 1) {
            fileKey = parts.slice(domainIndex + 1).join("/")
          }
        }
      }

      console.log("[v0] Extracted fileKey:", fileKey)

      const success = await deleteSpecialistProfileImage(userId, fileKey)

      if (success) {
        // Clear the profile image from state
        setProfileImage("")
        setEncryptedToken("profile_image", "")

        window.dispatchEvent(
          new CustomEvent("profileUpdated", {
            detail: {
              first_name: firstName,
              last_name: lastName,
              username: username,
              profile_image: "",
            },
          }),
        )
        // Note: Success toast is already handled in the API function
      } else {
        // Note: Error toast is already handled in the API function
        return
      }
    }

    // Clear local preview and selected image
    setSelectedImage(null)
    setImagePreview("")
    // Reset file input
    const fileInput = document.getElementById("profile-image") as HTMLInputElement
    if (fileInput) {
      fileInput.value = ""
    }
  }

  const handleUpdateProfile = async (e: React.FormEvent) => {
    e.preventDefault()
    const updatedFirstName = isEditingFirstName ? newFirstName : firstName
    const updatedLastName = isEditingLastName ? newLastName : lastName
    const updatedUsername = isEditingUsername ? newUsername : username
    const updatedEmail = isEditingEmail ? newEmail : email

    if (!updatedFirstName.trim() || !updatedLastName.trim() || !updatedUsername.trim() || !updatedEmail.trim()) {
      toast.error("All fields are required.")
      return
    }

    setUpdateLoading(true)
    const token = getDecryptedToken("AccessToken")
    if (!token) {
      toast.error("Authentication token missing.")
      setUpdateLoading(false)
      return
    }

    const updatedProfile: File | null = selectedImage ? selectedImage : null

    const result = await updateSpecialistProfile(token, {
      first_name: updatedFirstName,
      last_name: updatedLastName,
      email: updatedEmail,
      username: updatedUsername,
      profileImage: updatedProfile,
    })


    if (result.success) {
      setFirstName(updatedFirstName)
      setLastName(updatedLastName)
      setUsername(updatedUsername)
      setEmail(updatedEmail)

      let updatedProfileImageUrl = profileImage

      if (selectedImage) {
        if (result.data?.profile_image) {
          updatedProfileImageUrl = result.data.profile_image
        } else if (result.data?.data?.profile_image) {
          updatedProfileImageUrl = result.data.data.profile_image
          
        } else {
         
          // If no URL in response, fetch fresh profile data
          await fetchProfile()
          setUpdateLoading(false)
          return
        }

        setProfileImage(updatedProfileImageUrl)
        setImagePreview("") // Clear preview so it uses profileImage
        setSelectedImage(null)
      }

      setIsEditingFirstName(false)
      setIsEditingLastName(false)
      setIsEditingUsername(false)
      setIsEditingEmail(false)

      setEncryptedToken("first_name", updatedFirstName)
      setEncryptedToken("last_name", updatedLastName)
      setEncryptedToken("username", updatedUsername)
      if (selectedImage && updatedProfileImageUrl) {
        setEncryptedToken("profile_image", updatedProfileImageUrl)
      }

      toast.success("Profile updated successfully.")

      window.dispatchEvent(
        new CustomEvent("profileUpdated", {
          detail: {
            first_name: updatedFirstName,
            last_name: updatedLastName,
            username: updatedUsername,
            profile_image: updatedProfileImageUrl,
          },
        }),
      )
    } else {
      toast.error("Failed to update profile.")
    }
    setUpdateLoading(false)
  }

  const handleCancelPasswordEdit = () => {
    setCurrentPassword("")
    setNewPassword("")
    setConfirmPassword("")
    setNewPasswordErrors([])
    setConfirmPasswordErrors([])
    setPasswordsMatch(true)
    setPasswordSameAsOld(false)
    setIsEditingPassword(false)
  }

  const handleNewPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setNewPassword(password)
    setNewPasswordErrors(validatePassword(password))

    setPasswordSameAsOld(Boolean(currentPassword && password === currentPassword))

    if (confirmPassword) {
      setPasswordsMatch(password === confirmPassword)
    }
  }

  const handleConfirmPasswordChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const password = e.target.value
    setConfirmPassword(password)
    setConfirmPasswordErrors(validatePassword(password))
    setPasswordsMatch(newPassword === password)
  }

  const handleChangePassword = async () => {
    if (isEditingPassword) {
      if (!currentPassword.trim() || !newPassword.trim() || !confirmPassword.trim()) {
        toast.error("All password fields are required.")
        return
      }

      if (passwordSameAsOld) {
        return
      }

      if (newPasswordErrors.length > 0 || confirmPasswordErrors.length > 0) {
        toast.error("Please fix password validation errors before submitting.")
        return
      }

      if (!passwordsMatch) {
        toast.error("New password and confirm password do not match.")
        return
      }

      const success = await changePassword(currentPassword, newPassword, confirmPassword)

      if (success) {
        setCurrentPassword("")
        setNewPassword("")
        setConfirmPassword("")
        setNewPasswordErrors([])
        setConfirmPasswordErrors([])
        setPasswordsMatch(true)
        setPasswordSameAsOld(false)
        setIsEditingPassword(false)
      }
    } else {
      setIsEditingPassword(true)
    }
  }

  console.log("Profile data", {
    firstName,
    lastName,
    email,
    username,
    profileImage,
  })

  return (
    <div className="bg-white p-6 rounded-lg shadow-sm">
      {profileLoading ? (
        <div className="flex justify-center items-center min-h-[200px]">
          <div className="animate-spin rounded-full h-12 w-12 border-t-2 border-b-2 border-primary"></div>
        </div>
      ) : profileError ? (
        <div className="text-red-500 text-center py-4">{profileError}</div>
      ) : (
        <form onSubmit={handleUpdateProfile} className="grid grid-cols-1 md:grid-cols-2 gap-8">
          <div className="space-y-6">
            <h2 className="text-xl font-bold text-gray-800 mb-4">Profile</h2>

            {/* Profile Image Section */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700 mb-2">Profile Image</p>
                <div className="flex items-center justify-between">
                  {/* Image Preview */}
                  <div className="w-20 h-20 rounded-full border-2 border-gray-300 overflow-hidden bg-gray-100 flex items-center justify-center">
                    {imagePreview || profileImage ? (
                      <Image
                        src={imagePreview || profileImage}
                        alt="Profile"
                        width={1000}
                        height={1000}
                        className="w-full h-full object-cover"
                      />
                    ) : (
                      <svg className="w-8 h-8 text-gray-400" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                          strokeLinecap="round"
                          strokeLinejoin="round"
                          strokeWidth={2}
                          d="M16 7a4 4 0 11-8 0 4 4 0 018 0zM12 14a7 7 0 00-7 7h14a7 7 0 00-7-7z"
                        />
                      </svg>
                    )}
                  </div>

                  {/* Upload Controls */}
                  <div className="flex items-center gap-2">
                    <label
                      htmlFor="profile-image"
                      className="bg-[#EB6309] margin-[0px] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm text-center"
                    >
                      {imagePreview || profileImage ? "Change Image" : "Upload Image"}
                    </label>
                    <input
                      id="profile-image"
                      type="file"
                      accept="image/*"
                      onChange={handleImageUpload}
                      className="hidden"
                    />
                    {(imagePreview || selectedImage) && (
                      <button
                        type="button"
                        onClick={removeImage}
                        className="text-red-500 text-sm hover:text-red-700 transition-colors"
                      >
                        Remove
                      </button>
                    )}
                  </div>
                </div>
              </div>
            </div>

            {/* Remaining Fields */}
            {[
              {
                label: "First Name",
                value: firstName,
                editing: isEditingFirstName,
                setEditing: setIsEditingFirstName,
                newValue: newFirstName,
                setNewValue: setNewFirstName,
                setValue: setFirstName,
              },
              {
                label: "Last Name",
                value: lastName,
                editing: isEditingLastName,
                setEditing: setIsEditingLastName,
                newValue: newLastName,
                setNewValue: setNewLastName,
                setValue: setLastName,
              },
              {
                label: "Username",
                value: username,
                editing: isEditingUsername,
                setEditing: setIsEditingUsername,
                newValue: newUsername,
                setNewValue: setNewUsername,
                setValue: setUsername,
              },
              {
                label: "Primary Account Email*",
                value: email,
                editing: isEditingEmail,
                setEditing: setIsEditingEmail,
                newValue: newEmail,
                setNewValue: setNewEmail,
                setValue: setEmail,
              },
            ].map((field) => (
              <div key={field.label} className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
                <div>
                  <p className="font-medium text-gray-700">{field.label}</p>
                  {field.editing ? (
                    <input
                      type="text"
                      value={field.newValue}
                      onChange={(e) => field.setNewValue(e.target.value)}
                      className="mt-1 block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />
                  ) : (
                    <p className="text-gray-600">{field.value}</p>
                  )}
                </div>
                <button
                  type="button"
                  onClick={() =>
                    handleChangeField(
                      field.editing,
                      field.setEditing,
                      field.value,
                      field.setValue,
                      field.newValue,
                      field.setNewValue,
                    )
                  }
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  {field.editing ? "Save" : "Change"}
                </button>
              </div>
            ))}

            {/* Password Field */}
            <div className="flex justify-between items-center border-b-[1px] border-gray-200 pb-2">
              <div className="w-full">
                <p className="font-medium text-gray-700">Password</p>
                {isEditingPassword ? (
                  <div className="space-y-3 mt-2">
                    <input
                      type="password"
                      placeholder="Current Password"
                      value={currentPassword}
                      onChange={(e) => setCurrentPassword(e.target.value)}
                      className="block w-full px-3 py-2 border border-gray-300 rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500"
                    />

                    <div>
                      <input
                        type="password"
                        placeholder="New Password"
                        value={newPassword}
                        onChange={handleNewPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${
                          newPasswordErrors.length > 0 || passwordSameAsOld ? "border-red-300" : "border-gray-300"
                        }`}
                      />
                      {passwordSameAsOld && (
                        <p className="text-red-500 text-xs mt-1">
                          New password should be different from current password
                        </p>
                      )}
                      {newPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {newPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                    </div>

                    <div>
                      <input
                        type="password"
                        placeholder="Confirm New Password"
                        value={confirmPassword}
                        onChange={handleConfirmPasswordChange}
                        className={`block w-full px-3 py-2 border rounded-md shadow-sm focus:outline-none focus:ring-orange-500 focus:border-orange-500 ${
                          confirmPasswordErrors.length > 0 || !passwordsMatch ? "border-red-300" : "border-gray-300"
                        }`}
                      />
                      {confirmPasswordErrors.length > 0 && (
                        <div className="mt-1 space-y-1">
                          {confirmPasswordErrors.map((error, index) => (
                            <p key={index} className="text-red-500 text-xs">
                              {error}
                            </p>
                          ))}
                        </div>
                      )}
                      {confirmPassword && !passwordsMatch && confirmPasswordErrors.length === 0 && (
                        <p className="text-red-500 text-xs mt-1">Passwords do not match</p>
                      )}
                    </div>

                    <div className="flex gap-2">
                      <button
                        type="button"
                        onClick={handleChangePassword}
                        className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer text-sm"
                      >
                        Save
                      </button>
                      <button
                        type="button"
                        onClick={handleCancelPasswordEdit}
                        className="bg-gray-500 text-white px-4 py-1.5 rounded-full hover:bg-gray-600 transition-colors cursor-pointer text-sm"
                      >
                        Cancel
                      </button>
                    </div>
                  </div>
                ) : (
                  <p className="text-gray-600">{password}</p>
                )}
              </div>
              {!isEditingPassword && (
                <button
                  type="button"
                  onClick={handleChangePassword}
                  className="bg-[#EB6309] text-white px-4 py-1.5 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer"
                >
                  Change
                </button>
              )}
            </div>

            <div className="flex justify-end mt-8">
              <button
                type="submit"
                className="bg-[#EB6309] text-white px-6 py-2 rounded-full hover:bg-[#D45A08] transition-colors cursor-pointer disabled:opacity-60"
                disabled={updateLoading}
              >
                {updateLoading ? "Saving..." : "Save"}
              </button>
            </div>
          </div>
        </form>
      )}
    </div>
  )
}

export default SpecialistProfile
