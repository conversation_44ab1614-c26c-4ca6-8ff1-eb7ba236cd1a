"use client";

import Pagination from "@/components/reuseable/Pagination";
import React, { useState, useEffect } from "react";
// import { updateEmployeeStatus } from "@/utils/ApisHelperFunction";
// import {getDecryptedToken} from "@/app/lib/auth";
import StatusSelect from "./StatusSelect";

// const ChevronDown = () => (
//   <svg
//     className="w-4 h-4 text-gray-500"
//     fill="none"
//     stroke="currentColor"
//     strokeWidth={2}
//     viewBox="0 0 24 24"
//   >
//     <path strokeLinecap="round" strokeLinejoin="round" d="M19 9l-7 7-7-7" />
//   </svg>
// );

// const Spinner = () => (
//   <svg
//     className="animate-spin h-4 w-4 text-primary"
//     xmlns="http://www.w3.org/2000/svg"
//     fill="none"
//     viewBox="0 0 24 24"
//   >
//     <circle
//       className="opacity-25"
//       cx="12"
//       cy="12"
//       r="10"
//       stroke="currentColor"
//       strokeWidth={4}
//     />
//     <path
//       className="opacity-75"
//       fill="currentColor"
//       d="M4 12a8 8 0 018-8v8z"
//     />
//   </svg>
// );

/* ---------- types ---------- */
export interface StaffMember {
  id: string;
  name: string;
  accessLevel: string;
  email: string;
  phone?: string;
  profession?: string;
  status: "active" | "inactive";
}

interface StaffTableProps {
  data: StaffMember[];
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages?: number;
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (items: number) => void;
  onViewStaff: (id: string) => void;
  onEditStaff: (id: string) => void;
  onStatusChange?: (id: string, status: "active" | "inactive") => void;
  onSearch?: (searchTerm: string) => void;
}

// interface StatusSelectProps {
//   staff: StaffMember;
//   statusLoadingId: string | null;
//   setStatusLoadingId: Dispatch<SetStateAction<string | null>>;
//   onSearch?: (term: string) => void;
//   onStatusChange?: (id: string, status: "active" | "inactive") => void;
//   setFilteredData: Dispatch<SetStateAction<StaffMember[]>>;
// }

// const statusOptions = [
//   { value: "active", label: "Active", color: "bg-green-100 text-green-800" },
//   { value: "inactive", label: "Inactive", color: "bg-red-100 text-red-800" },
// ];

/* ---------- StatusSelect ---------- */
// const StatusSelect: React.FC<StatusSelectProps> = ({
//   staff,
//   statusLoadingId,
//   setStatusLoadingId,
//   onSearch,
//   onStatusChange,
//   setFilteredData,
// }) => {
//   const [open, setOpen] = useState(false);
//   const ref = useRef<HTMLDivElement>(null);

//   useEffect(() => {
//     const outsideClick = (e: MouseEvent) => {
//       if (ref.current && !ref.current.contains(e.target as Node)) setOpen(false);
//     };
//     document.addEventListener("mousedown", outsideClick);
//     return () => document.removeEventListener("mousedown", outsideClick);
//   }, []);

//   const handleSelect = async (newStatus: "active" | "inactive") => {
//     setOpen(false);
//     setStatusLoadingId(staff.id);

//     const token = getDecryptedToken("AccessToken");
//     if (!token) {
//       toast.error("Authentication token missing. Please log in again.");
//       setStatusLoadingId(null);
//       return;
//     }

//     try {
//       const result = await updateEmployeeStatus(staff.id, newStatus, token);
//       if (result && typeof result === "object" && "success" in result && result.success) {
//         toast.success("Status updated successfully");
//         if (!onSearch) {
//           setFilteredData((prev) =>
//             prev.map((s) => (s.id === staff.id ? { ...s, status: newStatus } : s))
//           );
//         }
//         onStatusChange?.(staff.id, newStatus);
//       } else {
//         toast.error("Failed to update status");
//       }
//     } catch  {
//     } finally {
//       setStatusLoadingId(null);
//     }
//   };

//   const currentLabel = statusOptions.find((o) => o.value === staff.status)?.label;

//   return (
//     <div className="relative w-full" onClick={(e) => e.stopPropagation()}>
//       <div ref={ref}>
//         <button
//           type="button"
//           disabled={statusLoadingId === staff.id}
//           onClick={() => setOpen((v) => !v)}
//           className={`pl-4 pr-10 py-2 text-xs rounded-full outline-none w-full appearance-none flex items-center justify-between transition-colors ${
//             staff.status === "active"
//               ? "bg-green-100 text-green-800"
//               : "bg-red-100 text-red-800"
//           } ${statusLoadingId === staff.id ? "opacity-50 cursor-not-allowed" : ""}`}
//         >
//           <span>{currentLabel}</span>
//           <span className="absolute inset-y-0 right-3 flex items-center pointer-events-none">
//             <ChevronDown />
//           </span>
//           {statusLoadingId === staff.id && (
//             <span className="absolute inset-y-0 right-8 flex items-center">
//               <Spinner />
//             </span>
//           )}
//         </button>

//         {open && (
//           <div className="absolute z-50 mt-1 w-[150px]">
//             <ul className="rounded-2xl bg-white shadow-lg ring-1 ring-[#ffe5d4] ring-opacity-5 focus:outline-none max-h-40 overflow-y-auto">
//               {statusOptions.map((opt) => (
//                 <li
//                   key={opt.value}
//                   onClick={() => handleSelect(opt.value as "active" | "inactive")}
//                   className={`cursor-pointer select-none relative py-2 pl-4 pr-10 rounded-2xl transition ${
//                     staff.status === opt.value
//                       ? `${opt.color} font-bold`
//                       : "text-gray-900"
//                   } hover:bg-primary/10 hover:text-primary`}
//                 >
//                   {opt.label}
//                 </li>
//               ))}
//             </ul>
//           </div>
//         )}
//       </div>
//     </div>
//   );
// };

/* ---------- Main table ---------- */
const StaffTable: React.FC<StaffTableProps> = ({
  data,
  currentPage,
  totalItems,
  itemsPerPage,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
  onViewStaff,
  onEditStaff,
  onStatusChange,
  onSearch,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [filteredData, setFilteredData] = useState<StaffMember[]>(data);
  const [statusLoadingId, setStatusLoadingId] = useState<string | null>(null);

  const handleSearchChange = (e: React.ChangeEvent<HTMLInputElement>) => {
    const value = e.target.value;
    setSearchTerm(value);
    if (onSearch) onSearch(value);
    else filterData(value);
  };

  const filterData = (term: string) => {
    if (!term.trim()) {
      setFilteredData(data);
      return;
    }
    const filtered = data.filter(
      (staff) =>
        staff.name.toLowerCase().includes(term.toLowerCase()) ||
        staff.email.toLowerCase().includes(term.toLowerCase()) ||
        staff.phone?.toLowerCase().includes(term.toLowerCase()) ||
        staff.profession?.toLowerCase().includes(term.toLowerCase()) ||
        staff.accessLevel.toLowerCase().includes(term.toLowerCase()),
    );
    setFilteredData(filtered);
  };

  useEffect(() => {
    if (searchTerm) filterData(searchTerm);
    else setFilteredData(data);
  }, [data]);

  const displayData = onSearch ? data : filteredData;

  return (
    <div className="w-full">
      {/* Search Field */}
      <div className="mb-4 flex items-center justify-end">
        <div className="relative w-3xs">
          <div className="absolute inset-y-0 left-0 flex items-center pl-3 pointer-events-none">
            <svg
              className="w-4 h-4 text-gray-500"
              aria-hidden="true"
              xmlns="http://www.w3.org/2000/svg"
              fill="none"
              viewBox="0 0 20 20"
            >
              <path
                stroke="currentColor"
                strokeLinecap="round"
                strokeLinejoin="round"
                strokeWidth="2"
                d="m19 19-4-4m0-7A7 7 0 1 1 1 8a7 7 0 0 1 14 0Z"
              />
            </svg>
          </div>
          <input
            type="text"
            className="bg-gray-50 border border-gray-300 text-gray-900 text-sm rounded-lg focus:ring-primary focus:border-primary block w-full pl-10 p-2.5"
            placeholder="Search staff by name, email,"
            value={searchTerm}
            onChange={handleSearchChange}
          />
        </div>
      </div>

      <div className="overflow-x-auto">
        <table className="min-w-full divide-y divide-gray-200">
          <thead className="bg-gray-50">
            <tr>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Staff Name
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Access Level
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Email
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Phone
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Profession
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Status
              </th>
              <th className="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                Actions
              </th>
            </tr>
          </thead>
          <tbody className="bg-white divide-y divide-gray-200">
            {displayData.length > 0 ? (
              displayData.map((staff) => (
                <tr
                  key={staff.id}
                  className="hover:bg-gray-50 cursor-pointer"
                  onClick={() => onViewStaff(staff.id)}
                >
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="flex items-center">
                      <div className="ml-4">
                        <div className="text-sm font-medium text-gray-900">
                          {staff.name}
                        </div>
                      </div>
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {staff.accessLevel}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{staff.email}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">{staff.phone}</div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <div className="text-sm text-gray-900">
                      {staff.profession}
                    </div>
                  </td>
                  <td className="px-6 py-4 whitespace-nowrap">
                    <StatusSelect
                      staff={staff}
                      statusLoadingId={statusLoadingId}
                      setStatusLoadingId={setStatusLoadingId}
                      onSearch={onSearch}
                      onStatusChange={onStatusChange}
                      setFilteredData={setFilteredData}
                    />
                  </td>

                  <td className="px-6 py-4 whitespace-nowrap text-sm font-medium">
                    <button
                      title="Edit staff"
                      className="text-primary hover:text-primary-dark mr-3 cursor-pointer"
                      onClick={(e) => {
                        e.stopPropagation();
                        onEditStaff(staff.id);
                      }}
                    >
                      <svg
                        xmlns="http://www.w3.org/2000/svg"
                        width="20"
                        height="20"
                        viewBox="0 0 20 20"
                        fill="none"
                      >
                        <path
                          d="M17.3287 7.26797L18.5487 6.04797C19.8157 4.78097 19.8157 2.71897 18.5487 1.45147C17.9352 0.838465 17.1197 0.501465 16.2502 0.501465C15.3807 0.501465 14.5647 0.838965 13.9517 1.45196L12.7322 2.67147L17.3287 7.26797ZM11.6717 3.73197L2.63723 12.7665C2.44473 12.959 2.29823 13.1965 2.21323 13.454L0.538232 18.5145C0.448732 18.7835 0.519232 19.08 0.719732 19.2805C0.863232 19.4235 1.05423 19.5 1.25023 19.5C1.32923 19.5 1.40873 19.4875 1.48623 19.462L6.54523 17.7865C6.80373 17.7015 7.04173 17.555 7.23423 17.362L16.2682 8.32797L11.6717 3.73197Z"
                          fill="#EB6309"
                        />
                      </svg>
                    </button>
                  </td>
                </tr>
              ))
            ) : (
              <tr>
                <td colSpan={7} className="px-6 py-4 text-center text-gray-500">
                  No staff members found matching your search.
                </td>
              </tr>
            )}
          </tbody>
        </table>
      </div>

    <Pagination
  currentPage={currentPage}
  totalItems={searchTerm ? filteredData.length : totalItems} 
  itemsPerPage={itemsPerPage}
  totalPages={searchTerm ? 1 : totalPages || 0} // Only one page if searching locally
  onPageChange={onPageChange}
  onItemsPerPageChange={onItemsPerPageChange}
/>
    </div>
  );
};

export default StaffTable;
