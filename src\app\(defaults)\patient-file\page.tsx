import { fetchApi, getServerCookies } from "@/api/getapis";
import PatientFile from "@/components/patient-file/PatientFile";
import { PatientFileData, Specialist } from "@/types/types";
import { API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import React from "react";

const Page = async ({
  searchParams,
}: {
  searchParams: Promise<{ id?: string }>;
}) => {
  const resolvedSearchParams = await searchParams;
  const patientId = resolvedSearchParams.id ?? "";

  console.log(patientId, 'Patient ID found')

  let patientData: PatientFileData | null = null;
  let specialistData: Specialist | null = null;

  if (patientId) {
    const role = await getServerCookies("Role");
    console.log("🚀 ~ page ~ role:", role);

    const baseRoute =
      role === "specialist"
        ? API_SERVER_ROUTES.PATIENT.GET_PATIENT_BY_ID_FOR_SPECIALIST
        : API_SERVER_ROUTES.PATIENT.GET_PATIENT_BY_ID;

    const url = `${baseRoute}/${patientId}`;

    const patientsArray = (await fetchApi(url)) as PatientFileData;
    const specialist = await fetchApi(
      `${API_SERVER_ROUTES.PATIENT.GET_SPECIALIST}/${patientId}/specialist`
    );

    patientData = patientsArray;
    specialistData =
      specialist && typeof specialist === "object" && "id" in specialist
        ? (specialist as Specialist)
        : null;
  }

  return (
    <>
      {patientData ? (
        <PatientFile
          data={patientData}
          patientId={patientId}
          specialist={specialistData as Specialist}
        />
      ) : (
        <div className="text-center py-10 text-gray-500">
          No patient data found.
        </div>
      )}
    </>
  );
};

export default Page;
