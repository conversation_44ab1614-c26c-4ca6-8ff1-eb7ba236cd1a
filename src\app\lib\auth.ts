import * as CryptoJ<PERSON> from "crypto-js";
import Cookies from "js-cookie";

/**
 * Stores an encrypted token in a cookie.
 * @param cookieName The name of the cookie.
 * @param token The token string to encrypt and store.
 */
export function setEncryptedToken(
  cookieName: string,
  token: string,
  rememberMe: boolean = false
) {
  if (!token) {
    console.error(`setEncryptedToken: invalid value for ${cookieName}`);
    return;
  }
  const encryptedToken = encryptToken(token);
  Cookies.set(cookieName, encryptedToken, {
    expires: rememberMe ? 10 : 1,
    path: "/",
  });
}

/**
 * Retrieves the raw (encrypted) token from a cookie.
 * @param cookieName The name of the cookie.
 * @returns The encrypted token string or undefined.
 */
export function getToken(cookieName: string): string | undefined {
  return Cookies.get(cookieName);
}

const cookiesToClear = [
  "AccessToken",
  "Email",
  "Role",
  "userid",
  "first_name",
  "last_name",
  "user_uuid",
  "username",
  "patientData",
  "patientId",
  "profile_image",
  "LongcasePrescriptionData",
  "ShortcasePrescriptionData",
  "retainerData",
];

/**
 * Removes a list of cookies.
 */
export function clearAllCookies() {
  cookiesToClear.forEach((cookieName) => {
    if (Cookies.get(cookieName)) {
      Cookies.remove(cookieName, {
        path: "/",
      });
    }
  });
}

/**
 * Encrypts a value using AES with a predefined key.
 * @param value The string to encrypt.
 * @returns The encrypted string.
 */
export const encryptToken = (value: string): string => {
  return CryptoJS.AES.encrypt(
    value,
    process.env.NEXT_PUBLIC_ENCRYPTION_KEY!
  ).toString();
};

/**
 * Decrypts a value using AES with a predefined key.
 * @param value The encrypted string.
 * @returns The decrypted string or null if decryption fails.
 */
export const decryptToken = (value: string): string | null => {
  try {
    const bytes = CryptoJS.AES.decrypt(
      value,
      process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "p7@Xr9k*2Mz#QuvL!e4C$0dT&gFnJqBs"
    );
    const decryptedValue = bytes.toString(CryptoJS.enc.Utf8);
    if (!decryptedValue) {
      console.error(
        "Decryption returned empty value. The token may be corrupted or the key might be wrong."
      );
      return null;
    }
    return decryptedValue;
  } catch (error) {
    console.error("Error during decryption:", error);
    return null;
  }
};

/**
 * Retrieves the token from a cookie, decrypting it first.
 * @param cookieName The name of the cookie.
 * @returns The decrypted token string or null.
 */
export const getDecryptedToken = (cookieName: string): string | null => {
  const encryptedValue = Cookies.get(cookieName);
  console.log("🚀 ~ getDecryptedToken ~ cookieName:", cookieName);
  console.log("🚀 ~ getDecryptedToken ~ encryptedValue:", encryptedValue);
  if (encryptedValue) {
    console.log(
      "🚀 ~ getDecryptedToken ~ encryptedValue:",
      decryptToken(encryptedValue)
    );
    return decryptToken(encryptedValue);
  } else {
    return null;
  }
};

// /////////////////
/**
 * Removes a cookie by name.
 * @param cookieName The name of the cookie to remove.
 */
export function removeCookieByName(cookieName: string) {
  if (Cookies.get(cookieName)) {
    Cookies.remove(cookieName, { path: "/" });
  }
}

// import Cookies from "js-cookie";
// import crypto from "crypto-js";

// export function storeToken(
//   value: string,
//   token: string,
//   rememberMe: boolean = false,
// ) {
//   Cookies.set(value, token, {
//     expires: rememberMe ? 10 : 1,
//     path: "/",
//   });
// }

// export function getToken(value: string) {
//   return Cookies.get(value);
// }

// const cookiesToClear = [
//   "AccessToken",
//   "Email",
//   "Role",
//   "userid",
//   "first_name",
//   "last_name",
//   "user_uuid",
//   "username",
//   "patientData",
//   "patientId",
//   "profile_image",
//   "LongcasePrescriptionData",
//   "ShortcasePrescriptionData",
//   "retainerData",
// ];

// export function clearAllCookies() {
//   cookiesToClear.forEach((cookieName) => {
//     if (Cookies.get(cookieName)) {
//       Cookies.remove(cookieName, {
//         path: "/",
//         maxAge: 0,
//       });
//     }
//   });
// }

// // Get encryption key from environment variable
// const ENCRYPTION_KEY =
//   process.env.NEXT_PUBLIC_ENCRYPTION_KEY || "p7@Xr9k*2Mz#QuvL!e4C$0dT&gFnJqBs";

// export const Encrytion = (value: string) => {
//   const encryptedValue = crypto.AES.encrypt(value, ENCRYPTION_KEY).toString();
//   return encryptedValue;
// };

// export const Decrytion = (value: string) => {
//   const decryptedValue = crypto.AES.decrypt(value, ENCRYPTION_KEY).toString(
//     crypto.enc.Utf8,
//   );
//   return decryptedValue;
// };

// const getAndDecryptCookie = (cookieName: string) => {
//   console.log(" getAndDecryptCookie called for:", cookieName)

//   const encryptedValue = Cookies.get(cookieName)
//   console.log(" Encrypted value exists:", encryptedValue ? "✓ Found" : "✗ Not found")

//   if (encryptedValue) {
//     try {
//       const decryptedValue = Decrytion(encryptedValue)
//       console.log(" Decrypted value for", cookieName + ":", decryptedValue || "empty/null")

//       // Special logging for userid to debug the undefined issue
//       if (cookieName === "userid") {
//         console.log(" User ID details:", {
//           encrypted: encryptedValue,
//           decrypted: decryptedValue,
//           type: typeof decryptedValue,
//           isEmpty: !decryptedValue,
//           isUndefined: decryptedValue === undefined,
//           isNull: decryptedValue === null,
//         })
//       }

//       return decryptedValue
//     } catch (error) {
//       console.error(" Error decrypting cookie", cookieName + ":", error)
//       return null
//     }
//   }

//   console.log(" No encrypted value found for:", cookieName)
//   return null
// }

// export function removeToken(key: string): void {
//   Cookies.remove(key);
// }
// export function logout(): void {
//   clearAllCookies();
//   window.location.href = "/login";
// }

// export function setRole(key: string, value: string) {
//   const encryptedValue = Encrytion(value);
//   if (key.toLowerCase() === "role") {
//     storeToken("Role", encryptedValue, true);
//   } else {
//     storeToken(key, encryptedValue, true);
//   }
// }

// export default getAndDecryptCookie;
