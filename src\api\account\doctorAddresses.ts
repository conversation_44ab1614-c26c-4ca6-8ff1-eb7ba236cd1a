import { API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import { DoctorAddressApiResponse } from "@/types/types";
import {getDecryptedToken} from "@/app/lib/auth";

// src/app/api/doctorAddresses.ts

export interface DoctorAddress {
  id: string;
  doctor_id: string;
  clinic_name: string;
  street_address: string;
  city: string;
  postal_code: string;
  phone_number: string;
  created_at: string;
  updated_at: string;
  address_type: "ship_to" | "bill_to" | string;
}

export async function fetchDoctorAddresses(): Promise<DoctorAddress[] | null> {
  const token = getDecryptedToken("AccessToken");
  const url = API_SERVER_ROUTES.ADRESSES.GET_ADRESS
  try {
    const response = await fetch(url,
      {
        headers: {
          accept: "application/json",
          Authorization: `Bearer ${token}`,
        },
      },
    );

    if (!response.ok) {
      throw new Error(`HTTP error! status: ${response.status}`);
    }

    const data: Doctor<PERSON>ddressApiResponse = await response.json();

    if (data.success) {
      return data.data as unknown as <PERSON><PERSON><PERSON><PERSON>[];
    } else {
      return [];
    }
  } catch (error) {
    console.log("🚀 ~ fetchDoctorAddresses ~ error:", error)
    return [];
  }
}
