"use client";
import React from "react";
import { z } from "zod";
import { FieldErrors, useForm } from "react-hook-form";
import { zodResolver } from "@hookform/resolvers/zod";
import {
  AnteriorCrossBiteSchema,
  attachmentRestrictionSchema,
  biteRamps,
  ExtractionSchema,
  midline,
  movementRestrictionSchema,
  overbiteSchema,
  overjetSchema,
  PosteriorCrossBiteSchema,
  sagittalRelationship,
  spacingSchema,
  specialNoteSchema,
  stepOneSchema,
  TeethInformationSchema,
} from "../ZodSchemas";
import ArchToTreat_1 from "../ArchToTreat_1";
import { toast } from "react-toastify";
import { useEffect, useState } from "react";
import MovementResctiction_2 from "../MovementResctiction_2";
import AttachmentResctriction_3 from "../AttachmentRestriction_3";
import Overjet from "../Overjet";
import Overbite from "../OverBite";
import BiteRamps from "../BiteRamps_7";
import MidLine from "../Midline_8";
import Spacing_9 from "../Spacing_9";
import SpecialNote_10 from "../SpecialNote_10";
import PosteriorCrossBite from "../PosteriorCrossBite";
import AnteriorCrossBite from "../AnteriorCrossBite";
import TeethInformation from "../TeethInformation";
import Extraction from "../Extraction";
import { useRouter } from "next/navigation";
import SagitalrRelationShip from "../AnteriorPosterior_4";
import FormWrapper from "@/components/reuseable/FormWrapper";
import { submitCasePrescription } from "@/utils/ApisHelperFunction";
import { getDecryptedToken, setEncryptedToken } from "@/app/lib/auth";
import { PatientCaseData } from "@/types/types";

const PrescriptionSchema = z.object({
  archToTreat: stepOneSchema,
  movementResctriction: movementRestrictionSchema,
  attachmentRestrictionSchema: attachmentRestrictionSchema,
  sagitalrRelationShip: sagittalRelationship,
  overjet: overjetSchema,
  overbite: overbiteSchema,
  biteRamps: biteRamps,
  midline: midline,
  spacing: spacingSchema,
  specialNote: specialNoteSchema,
  posteriorCrossBite: PosteriorCrossBiteSchema,
  anteriorCrossBite: AnteriorCrossBiteSchema,
  teethInformation: TeethInformationSchema,
  extraction: ExtractionSchema,
});

const defaultCasePrescriptionData: PrescriptionSchemaType = {
  archToTreat: { option: "", value: "" },
  movementResctriction: {
    option: "none",
    restrictedTeeth: [],
    primaryDefinationTeeth: [],
  },
  attachmentRestrictionSchema: {
    option: "none",
    restrictedTeeth: [],
    primaryDefinationTeeth: [],
  },
  sagitalrRelationShip: {
    right: {
      option: "",
      improveCanine: "",
      improveMolar: "",
      improveMolarNote: "",
      improveCanineNote: "",
      improveCanineOption: "",
      improveMolarOption: "",
    },
    left: {
      option: "",
      improveCanine: "",
      improveMolar: "",
      improveCanineNote: "",
      improveMolarNote: "",
      improveCanineOption: "",
      improveMolarOption: "",
    },
  },
  overjet: { option: "showResultingOverjet" },
  overbite: {
    option: "",
    upper: {
      options: [],
      others: false,
      otherNote: "",
      anteriorBiteRamps: [],
    },
    lower: {
      options: [],
      otherNote: "",
      others: false,
      anteriorBiteRamps: [],
    },
  },
  biteRamps: {
    option: "",
    selectedTooth: [],
  },
  midline: {
    option: "showResultingMidline",
    midlineIPRUpper: false,
    midlineIPRLower: false,
    upperMidlineShift: "none",
    lowerMidlineShift: "none",
  },
  spacing: {
    option: "Close all spaces",
    upper: "",
    lower: "",
    note: "",
    resolveUpper: {
      expand: "none",
      procline: "none",
      iprAnterior: "none",
      molarDistalization: [],
    },
    resolveLower: {
      expand: "none",
      procline: "none",
      iprAnterior: "none",
      molarDistalization: [],
    },
  },
  specialNote: {
    note: "",
  },
  posteriorCrossBite: {
    option: "",
    location: [] as ("right" | "left")[],
  },
  anteriorCrossBite: {
    option: "",
    location: [],
  },
  teethInformation: {
    missingTeethOption: "none",
    missingTeeth: [],
    primaryDefinationOption: "none",
    primaryDefinationTeeth: [],
  },
  extraction: {
    option: "none",
    extractionTeeth: [],
    primaryDefinationTeeth: [],
  },
};

export type PrescriptionSchemaType = z.infer<typeof PrescriptionSchema>;

interface CasePrescriptionProps {
  patientData: PatientCaseData;
}
const LongVersion = ({ patientData }: CasePrescriptionProps) => {
  const [defaultValues, setDefaultValues] = useState<PrescriptionSchemaType>(
    defaultCasePrescriptionData,
  );
  const [isReady, setIsReady] = useState(false);

  const {
    register,
    watch,
    handleSubmit,
    setValue,
    reset,
    formState: { errors },
  } = useForm<PrescriptionSchemaType>({
    resolver: zodResolver(PrescriptionSchema),
    defaultValues: defaultValues,
    mode: "onChange",
  });

  useEffect(() => {
    const localData = localStorage.getItem("casePrescriptionData");
    if (patientData?.data !== null) {
      setDefaultValues(patientData?.data as PrescriptionSchemaType);
      reset(patientData?.data as PrescriptionSchemaType);
    } else if (localData) {
      try {
        const parsedLocal = JSON.parse(localData);
        setDefaultValues(parsedLocal as PrescriptionSchemaType);
        reset(parsedLocal as PrescriptionSchemaType);
      } catch {
        // handle error
        reset(defaultCasePrescriptionData);
      }
    } else {
      reset(defaultCasePrescriptionData);
    }
    setIsReady(true);
  }, [reset, patientData?.data]);
  const router = useRouter();
  const data = watch();

  useEffect(() => {}, [data]);

  const onSubmit = async (data: PrescriptionSchemaType) => {
    console.log("Valid form data:", data);

    try {
      const token = getDecryptedToken("AccessToken");
      const patientId = getDecryptedToken("patientId");
      const version = "long";

      if (!token) {
        toast.error("Login is required.");
        return;
      }

      if (!patientId) {
        return;
      }
      const casedata = {
        data,
        version: version,
      };
      setEncryptedToken("LongcasePrescriptionData", JSON.stringify(casedata));

      const result = await submitCasePrescription(
        token,
        patientId,
        casedata,
        version,
      );

      if (result) {
        // Success
        localStorage.removeItem("shortCasePrescriptionData");
        localStorage.setItem("casePrescriptionData", JSON.stringify(data));
        toast.success("Case prescription submitted successfully!");
        router.push("/summary");
      } else {
        // Error
        toast.error("Failed to submit case prescription. Please try again.");
      }
    } catch (error) {
      toast.error(`Error submitting case prescription: ${error}`);
    }
  };

  const onError = (errors: FieldErrors<PrescriptionSchemaType>) => {
    toast.error("Please fill all the required fields");
    console.log("❌ Form validation errors:", errors);
  };

  if (!isReady) return <div>Loading...</div>;

    return (
        <FormWrapper classNames="!grid-cols-1" onSubmit={handleSubmit(onSubmit, onError)} onBack={() => {router.back()}}>
            <div className="">
                <div className="flex flex-col gap-8">
                    {/* step 1 */}
                    <ArchToTreat_1<PrescriptionSchemaType> number='1.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 2 */}
                    <TeethInformation<PrescriptionSchemaType> number='2.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 3 */}
                    <MovementResctiction_2<PrescriptionSchemaType> number='3.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 4 */}
                    <AttachmentResctriction_3<PrescriptionSchemaType> number="4." register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 5 */}
                    <SagitalrRelationShip<PrescriptionSchemaType> number="5." register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 6 */}
                    <Overjet<PrescriptionSchemaType> number="6." register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 7 */}
                    <Overbite<PrescriptionSchemaType> number='7.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 8 */}
                    <AnteriorCrossBite<PrescriptionSchemaType> number='8.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 10 */}
                    <PosteriorCrossBite<PrescriptionSchemaType> number='9.' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 11 */}
                    <MidLine<PrescriptionSchemaType> number='1.0' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 12 */}
                    <Spacing_9<PrescriptionSchemaType> number='1.1' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 13 */}
                    <Extraction<PrescriptionSchemaType> number='1.2' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 15 */}
                    <BiteRamps<PrescriptionSchemaType> number='1.3' register={register} watch={watch} errors={errors} setValue={setValue} />
                    {/* step 16 */}
                    <SpecialNote_10<PrescriptionSchemaType> number='1.4' register={register} watch={watch} errors={errors} setValue={setValue} />

                </div>
            </div>
        </FormWrapper>
    )
}


export default LongVersion;
