import { API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import { fetchApi } from "../getapis";
import { EmployeeApiResponse } from "@/types/types";

export interface PaginatedEmployees {
  employees: unknown[];
  currentPage: number;
  perPage: number;
  totalItems: number;
  totalPages: number;
}

export const fetchSpecialistEmployees = async (
  page: number = 1,
  limit: number = 10,
): Promise<PaginatedEmployees | null> => {
  const url = `${API_SERVER_ROUTES.SPECIALIST_EMPLOYEE.GET_EMPLOYEES}?page=${page}&limit=${limit}`;
  type EmployeesApiResponse = {
    data: EmployeeApiResponse[];
    currentPage: number;
    perPage: number;
    totalItems: number;
    totalPages: number;
  };
  const d = await fetchApi<EmployeesApiResponse>(url);
  if (!d) return null;
  return {
    employees: d.data || [],
    currentPage: d.currentPage || 1,
    perPage: d.perPage || 10,
    totalItems: d.totalItems || 0,
    totalPages: d.totalPages || 1,
  };
};

