import React from "react";

export default function Circle() {
  return (
    <svg
      xmlns="http://www.w3.org/2000/svg"
      width="20"
      height="20"
      viewBox="0 0 20 20"
      fill="none"
    >
      <path
        d="M9.99967 2.08334C10.1101 2.08177 10.2197 2.10217 10.3222 2.14335C10.4247 2.18453 10.518 2.24566 10.5966 2.3232C10.6752 2.40074 10.7377 2.49313 10.7803 2.59501C10.8229 2.69689 10.8449 2.80623 10.8449 2.91667C10.8449 3.02711 10.8229 3.13644 10.7803 3.23832C10.7377 3.3402 10.6752 3.4326 10.5966 3.51014C10.518 3.58767 10.4247 3.64881 10.3222 3.68998C10.2197 3.73116 10.1101 3.75156 9.99967 3.75C6.30797 3.75 3.33301 6.72497 3.33301 10.4167C3.33301 14.1084 6.30797 17.0833 9.99967 17.0833C11.9148 17.0833 13.6318 16.2807 14.8483 14.9927C14.9229 14.911 15.013 14.8449 15.1134 14.7984C15.2137 14.7519 15.3224 14.7258 15.4329 14.7217C15.5435 14.7176 15.6537 14.7356 15.7573 14.7746C15.8608 14.8135 15.9556 14.8727 16.036 14.9486C16.1164 15.0246 16.1809 15.1158 16.2257 15.2169C16.2706 15.3181 16.2948 15.4271 16.297 15.5377C16.2993 15.6483 16.2795 15.7583 16.2388 15.8611C16.1981 15.964 16.1374 16.0577 16.0601 16.1369C14.5416 17.7447 12.3821 18.75 9.99967 18.75C5.40721 18.75 1.66634 15.0091 1.66634 10.4167C1.66634 5.82421 5.40721 2.08334 9.99967 2.08334ZM12.8789 2.65462C13.0999 2.65462 13.3119 2.74242 13.4682 2.8987C13.6244 3.05498 13.7122 3.26694 13.7122 3.48796C13.7122 3.70897 13.6244 3.92093 13.4682 4.07721C13.3119 4.23349 13.0999 4.32129 12.8789 4.32129C12.6579 4.32129 12.4459 4.23349 12.2897 4.07721C12.1334 3.92093 12.0456 3.70897 12.0456 3.48796C12.0456 3.26694 12.1334 3.05498 12.2897 2.8987C12.4459 2.74242 12.6579 2.65462 12.8789 2.65462ZM15.3097 4.28548C15.5233 4.28548 15.7372 4.36631 15.8997 4.52881H15.8957C16.2248 4.85381 16.2207 5.38301 15.8957 5.70801C15.7332 5.87051 15.5206 5.94971 15.3081 5.94971C15.0956 5.94971 14.883 5.87051 14.7205 5.70801H14.7165C14.3915 5.37884 14.3955 4.85381 14.7205 4.52881C14.883 4.36631 15.0962 4.28548 15.3097 4.28548ZM16.939 6.71631C17.2644 6.71983 17.5696 6.91433 17.7039 7.23308C17.8789 7.65808 17.679 8.14613 17.2498 8.32113C17.1457 8.3628 17.0412 8.38298 16.9333 8.38298C16.6079 8.38298 16.2951 8.19193 16.1618 7.8711C15.9868 7.4461 16.1868 6.95814 16.6118 6.77897C16.7191 6.73522 16.8305 6.71514 16.939 6.71631ZM17.4997 9.5874C17.9584 9.5874 18.333 9.9624 18.333 10.4207C18.333 10.8832 17.9584 11.2541 17.4997 11.2541C17.0376 11.2541 16.6663 10.8791 16.6663 10.4207C16.6663 9.95824 17.0413 9.5874 17.4997 9.5874ZM16.9251 12.4561C17.0334 12.4555 17.1435 12.4764 17.2498 12.5212C17.6748 12.6962 17.8749 13.1834 17.6999 13.6084C17.5665 13.9292 17.2584 14.1252 16.9292 14.1252C16.825 14.1252 16.716 14.1043 16.6118 14.0584C16.1868 13.8834 15.9827 13.3962 16.1577 12.9712C16.2921 12.6524 16.6003 12.4578 16.9251 12.4561Z"
        fill="#43A047"
      />
    </svg>
  );
}
