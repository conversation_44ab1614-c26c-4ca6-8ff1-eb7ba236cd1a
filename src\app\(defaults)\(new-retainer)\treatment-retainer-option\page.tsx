import { fetchApi, getServerCookies } from "@/api/getapis";
import TreatmentRetainerOption from "@/components/treatment-retainer-option/TreatmentRetainerOption";
import { PatientFileData, PlansResponse } from "@/types/types";
import { API_ROUTES, API_SERVER_ROUTES } from "@/utils/ApiRoutes";
import React from "react";
export const dynamic = "force-dynamic";
const page = async () => {
  const response = (await fetchApi(
    `${API_SERVER_ROUTES.PLAN.GET_PLANS}`,
  )) as PlansResponse;
  const patientId = await getServerCookies("patientId");
  let patientData: PatientFileData | null = null;

  if (patientId) {
    const patientsArray = await fetchApi(
      `${API_ROUTES.PATIENT.GET_PATIENT_BY_ID}/${patientId}`,
    );
    patientData = patientsArray as PatientFileData;
    console.log("🚀 ~ page ~ patientsArray:", patientsArray);
    console.log("🚀 ~ page ~ patientData:", patientData);
  } else {
    console.log("🚀 ~ page ~ patientData:", patientData);
  }
  return (
    <div>
      <TreatmentRetainerOption
        data={response.plans}
        patientData={patientData}
      />
    </div>
  );
};

export default page;
