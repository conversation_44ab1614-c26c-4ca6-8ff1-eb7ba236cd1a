import React from "react";

interface PaginationProps {
  currentPage: number;
  totalItems: number;
  itemsPerPage: number;
  totalPages: number; // Add this property
  onPageChange: (page: number) => void;
  onItemsPerPageChange: (itemsPerPage: number) => void; // Add this property
}

const Pagination: React.FC<PaginationProps> = ({
  currentPage,
  totalItems,
  itemsPerPage,
  totalPages,
  onPageChange,
  onItemsPerPageChange,
}) => {
  const handlePageChange = (page: number) => {
    if (page >= 1 && page <= totalPages) {
      onPageChange(page);
    }
  };

  return (
    <div className="flex items-center justify-between my-3 mx-2">
      {/* Left Section: Rows per page and entries details */}
      <div className="flex items-center space-x-4">
        {/* Rows Per Page Dropdown */}
        <div className="flex items-center space-x-2">
          <span className="text-sm text-gray-600">Rows per page:</span>
          <select
            className="border border-gray-300 cursor-pointer rounded px-2 py-1 text-sm"
            value={itemsPerPage}
            onChange={(e) => onItemsPerPageChange(Number(e.target.value))}
          >
            <option className="cursor-pointer" value={10}>
              10
            </option>
            <option className="cursor-pointer" value={20}>
              20
            </option>
            <option className="cursor-pointer" value={30}>
              30
            </option>
            <option className="cursor-pointer" value={50}>
              50
            </option>
          </select>
        </div>

        {/* Display Range */}
        <div className="text-sm text-gray-600">
          Showing {(currentPage - 1) * itemsPerPage + 1} to{" "}
          {Math.min(currentPage * itemsPerPage, totalItems)} of {totalItems}{" "}
          entries
        </div>
      </div>

      {/* Right Section: Pagination Controls */}
      <div className="flex items-center space-x-1">
        {/* First Page Button */}
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === 1 ? "text-gray-400" : "text-[#EB6309] bg-[#FDE8D4]"
          } bg-[#FDE8D4]`}
          onClick={() => handlePageChange(1)}
          disabled={currentPage === 1}
        >
          «
        </button>

        {/* Previous Page Button */}
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === 1 ? "text-gray-400" : "text-[#EB6309] bg-[#FDE8D4]"
          } bg-[#FDE8D4]`}
          onClick={() => handlePageChange(currentPage - 1)}
          disabled={currentPage === 1}
        >
          ‹
        </button>

        {/* Page Numbers */}
        {Array.from({ length: totalPages }, (_, index) => index + 1).map(
          (page) => (
            <button
              key={page}
              className={`w-8 h-8 cursor-pointer text-center rounded-full text-sm ${
                currentPage === page
                  ? "bg-[#EB6309] text-white"
                  : "text-[#EB6309] bg-[#FDE8D4]"
              }`}
              onClick={() => handlePageChange(page)}
            >
              {page}
            </button>
          ),
        )}

        {/* Next Page Button */}
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === totalPages
              ? "text-gray-400"
              : "text-[#EB6309] bg-[#FDE8D4]"
          } bg-[#FDE8D4]`}
          onClick={() => handlePageChange(currentPage + 1)}
          disabled={currentPage === totalPages}
        >
          ›
        </button>

        {/* Last Page Button */}
        <button
          className={`w-8 h-8 cursor-pointer text-center rounded-full text-xl ${
            currentPage === totalPages
              ? "text-gray-400"
              : "text-[#EB6309] bg-[#FDE8D4]"
          } bg-[#FDE8D4]`}
          onClick={() => handlePageChange(totalPages)}
          disabled={currentPage === totalPages}
        >
          »
        </button>
      </div>
    </div>
  );
};

export default Pagination;
