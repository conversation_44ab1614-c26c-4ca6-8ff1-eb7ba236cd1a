"use client";
import RoundCheckbox from "@/components/reuseable/RoundCheckbox";
import { useState } from "react";

const InvoiceBilling = () => {
  const [billindAddress, setBillingAddress] = useState<string>();
  console.log(billindAddress);
  return (
    <div className=" p-6 bg-white rounded-xl col-span-1">
      <h3 className="font-bold text-dark text-[1.1rem] mb-3">
        Invoice Billing Address
      </h3>
      <RoundCheckbox
        id="include-secondary"
        label="AlOqair street, Al Hofuf, 36365, SA (#1434279)"
        value="AlOqair street, Al Hofuf, 36365, SA (#1434279)"
        onChange={(e) => setBillingAddress(e.target.value)}
      />
    </div>
  );
};

export default InvoiceBilling;
